﻿using Azure.Core;
using Azure.Identity;
using Microsoft.AspNetCore.Server.Kestrel.Core;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static TokenCredential? _azureCredential;

        internal static void ConfigureKestrel(this WebApplicationBuilder builder)
        {
            builder.WebHost.ConfigureKestrel(o =>
            {
                o.Limits.MinRequestBodyDataRate = new MinDataRate(100, TimeSpan.FromSeconds(60));
            });
        }

        internal static void ConfigureEnvironment(this WebApplicationBuilder builder)
        {
            var envName = builder.Environment.IsEnvironment("Local") ?
                "US-DEV" :
                builder.Environment.EnvironmentName.ToUpper();

            builder.Configuration
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile($"appsettings.{envName}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables();

            _azureCredential = builder.Environment.IsEnvironment("Local")
                ? new AzureCliCredential()
                : new ManagedIdentityCredential();

           builder.Configuration.AddAzureKeyVault(
                new Uri(builder.Configuration["AzureKeyVault:Endpoint"]!), _azureCredential);
        }

        internal static void ConfigureCors(this IServiceCollection services)
        {
            services.AddCors(options =>
            {
                options.AddDefaultPolicy(builder =>
                {
                    builder.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader();
                });
            });
        }

        internal static void ConfigureHostOptions(this IServiceCollection services)
        {
            services.Configure<HostOptions>(options =>
            {
                options.BackgroundServiceExceptionBehavior = BackgroundServiceExceptionBehavior.Ignore;
            });
        }

        internal static void ValidateConfiguration(this WebApplicationBuilder builder)
        {
            var requiredKeys = new[]
            {
                "AzureStorage:Endpoints:Default",
                "AzureStorage:Container",
                "AzureStorage:ExpirationPolicy",
                "Conversion:Endpoint",
                "Conversion:Key",
                "AzureSearch:Endpoints:Default",
                "AzureSearch:Index",
                "DefaultSettings:Top",
                "DefaultSettings:MinimumContextLength",
                "AzureDocIntel:Endpoint",
                "ChunkSettings:SectionOverlap",
                "Translation:Endpoint",
                "AzureStorage:Translation",
                "Background:QueueSettings:Workers",
                "Background:QueueSettings:BasePath",
                "AzureOpenAI:Endpoint",
                "GallagherAI-OpenAIKey",
                "AzureOpenAI:Deployment",
                "AzureOpenAI:Embeddings",
                "AzureOpenAI:Dimensions"
            };

            foreach (var key in requiredKeys)
            {
                ArgumentNullException.ThrowIfNullOrEmpty(builder.Configuration[key], $"{key} cannot be null or empty");
            }
        }
    }
}