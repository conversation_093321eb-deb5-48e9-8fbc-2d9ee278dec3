import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { RecentChatState } from "../interfaces";

const initialState: RecentChatState[] = [];

const recentChatSlice = createSlice({
  name: "recentChat",
  initialState,
  reducers: {
    setRecentChat: (state, action: PayloadAction<{ id: string, text: string }>) => {
      const { id, text } = action.payload;
      const cleanedText = text.split('\n<')[0];
      state.push({ id, text: cleanedText.trim() });
    },
    clearRecentChat: () => {
      return [];
    },
    updateRecentChat: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const index = state.findIndex(chat => chat.id === id);
      if (index !== -1) {
      const [chat] = state.splice(index, 1);
      state.unshift(chat);
      }
    }
  },
});

export const { setRecentChat, clearRecentChat, updateRecentChat} = recentChatSlice.actions;

export default recentChatSlice.reducer;
