﻿using Azure;
using Azure.AI.FormRecognizer.DocumentAnalysis;
using Backend.Models;
using Backend.Services;
using Microsoft.Extensions.Logging;
using Moq;
using System.Text;
using System.Reflection;

namespace BackendUnitTests
{
    public class DocumentIntelligenceTests
    {
        private readonly Mock<DocumentAnalysisClient> _mockDocClient;
        private readonly Mock<ILogger<DocumentIntelligenceService>> _mockLogger;
        private readonly DocumentIntelligenceService _service;

        public DocumentIntelligenceTests()
        {
            _mockDocClient = new Mock<DocumentAnalysisClient>();
            _mockLogger = new Mock<ILogger<DocumentIntelligenceService>>();
            _service = new DocumentIntelligenceService(_mockDocClient.Object,
                                                       _mockLogger.Object,
                                                       130);
        }

        private IEnumerable<Section> InvokeSplitIntoSections(string text,
                                                             string blob,
                                                             string oid,
                                                             string workspaceId,
                                                             int start,
                                                             int i,
                                                             int max = 20_000)
        {
            MethodInfo methodInfo = typeof(DocumentIntelligenceService)
                .GetMethod("SplitIntoSections", BindingFlags.NonPublic | BindingFlags.Instance)!;
            return (IEnumerable<Section>)methodInfo.Invoke(
                _service, new object[] { text, blob, oid, workspaceId, start, i, max })!;
        }

        /******************************************
        *                Tests
        ******************************************/

        [Fact]
        public void CreateSections_EmptyPageDetails_ReturnsEmptyList()
        {
            // Arrange
            var pageDetails = new List<PageDetail>();
            var oid = "test-oid";
            var workspaceId = "test-workspace";
            var blob = "test-blob";

            // Act
            var sections = _service.CreateSections(
                pageDetails.AsReadOnly(), oid, workspaceId, blob).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.Empty(sections);
        }

        [Fact]
        public void ReplaceTablesWithHtml_NoTables_ReturnsOriginalText()
        {
            // Arrange
            var result = DocumentAnalysisModelFactory.AnalyzeResult(
                content: "Sample document content",
                pages: new List<DocumentPage>
                {
                    DocumentAnalysisModelFactory.DocumentPage(
                        pageNumber: 1,
                        width: 8.5f,
                        height: 11f,
                        unit: DocumentPageLengthUnit.Inch,
                        spans: new List<DocumentSpan> {
                            DocumentAnalysisModelFactory.DocumentSpan(0, 23)
                        }
                    )
                },
                tables: new List<DocumentTable>()
            );

            int pageIndex = 0;
            int[] tableChars = new int[23];
            var tablesOnPage = new List<DocumentTable>
            {
                DocumentAnalysisModelFactory.DocumentTable(
                    rowCount: 1,
                    columnCount: 1,
                    cells: new List<DocumentTableCell>
                    {
                        DocumentAnalysisModelFactory.DocumentTableCell(
                            rowIndex: 0,
                            columnIndex: 0,
                            rowSpan: 1,
                            columnSpan: 1,
                            content: "Sample cell content",
                            spans: new List<DocumentSpan>
                            {
                                DocumentAnalysisModelFactory.DocumentSpan(0, 18)
                            }
                        )
                    },
                    spans: new List<DocumentSpan>
                    {
                        DocumentAnalysisModelFactory.DocumentSpan(0, 18)
                    }
                )
            };

            // Act
            var pageText = InvokeReplaceTablesWithHtml(result, pageIndex, tableChars, tablesOnPage);

            // Assert
            Assert.Equal("<table><tr><td>Sample cell content</td></tr></table> ", pageText);
        }

        [Fact]
        public void CreateSections_RandomTests()
        {
            // Arrange
            var random = new Random();
            for (int i = 0; i < 100; i++)
            {
                var pageDetails = GenerateRandomPageDetails(random, random.Next(1, 40));
                string oid = Guid.NewGuid().ToString();
                string workspaceId = Guid.NewGuid().ToString();
                string blob = GenerateRandomText(random, 10, "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");

                // Act
                var sections = _service.CreateSections(pageDetails.AsReadOnly(), oid, workspaceId, blob).ToList();

                // Assert
                Assert.NotNull(sections);
                Assert.All(sections, section =>
                {
                    Assert.StartsWith(blob, section.Id);
                    Assert.Equal(workspaceId, section.Workspace);
                    Assert.StartsWith(blob, section.SourceFile);
                });
            }
        }

        [Fact]
        public void SplitIntoSections_Max20()
        {
            // Arrange
            string text = "This is a test. This is another test. Some more text.";
            string blob = "test-blob";
            string oid = "test-oid";
            string workspaceId = "test-workspace";
            int start = 0;
            int i = 1;
            int max = 20;

            // Act
            var sections = InvokeSplitIntoSections(text, blob, oid, workspaceId, start, i, max).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.True(sections.Count > 1);
            Assert.Contains(sections, s => s.Content.Contains("This is a test."));
            Assert.Contains(sections, s => s.Content.Contains("Some more text."));
        }

        [Fact]
        public void SplitIntoSections_CreatesSections()
        {
            // Arrange
            string text = "This is a test. This is another test. Some more text.";
            string blob = "test-blob";
            string oid = "test-oid";
            string workspaceId = "test-workspace";
            int start = 0;
            int i = 1;
            int max = 20_000;

            // Act
            var sections = InvokeSplitIntoSections(text, blob, oid, workspaceId, start, i, max).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.True(sections.Count == 1);
            Assert.Contains(sections, s => s.Content.Contains("This is a test. This is another test. Some more text."));
        }

        [Fact]
        public void SplitIntoSections_WorksWithNoText()
        {
            // Arrange
            string text = "";
            string blob = "";
            string oid = "";
            string workspaceId = "";
            int start = 0;
            int i = 1;
            int max = 20_000;

            // Act
            var sections = InvokeSplitIntoSections(text, blob, oid, workspaceId, start, i, max).ToList();

            // Assert
            Assert.NotNull(sections);
        }

        [Fact]
        public void FindSplitPoint_SplitsAtASentenceEnding()
        {
            // Arrange
            string text = "This is a test. This is another test. Some more text.";
            int mid = text.Length / 2;

            // Act
            int splitPoint = InvokeFindSplitPoint(text, mid);

            // Assert
            Assert.Equal(36, splitPoint); // Split point should be after the second sentence
        }

        [Fact]
        public void FindSplitPoint_ReturnsMidIfNoSuitableSplitPoint()
        {
            // Arrange
            string text = "Thisisaverylongwordwithoutanybreaks";
            int mid = text.Length / 2;

            // Act
            int splitPoint = InvokeFindSplitPoint(text, mid);

            // Assert
            Assert.Equal(mid, splitPoint); // Split point should be the middle of the text
        }

        [Fact]
        public void FindSplitPoint_IsMidOrDoesntExceedMax_RandomTests()
        {
            // Arrange
            var random = new Random();
            for (int i = 0; i < 100; i++)
            {
                string text = GenerateRandomText(random, 40_000);
                int mid = text.Length / 2;
                int max = random.Next(100, 40_000);

                // Act
                int splitPoint = InvokeFindSplitPoint(text, mid, max);

                // Assert
                Assert.True(splitPoint <= max || splitPoint == mid,
                    $"Split point {splitPoint} exceeds max {max} or is not {mid} for text: {text}");
            }
        }

        [Fact]
        public async Task GetDocumentTextAsync_ReturnsPageDetails()
        {
            // Arrange
            var stream = new MemoryStream(Encoding.UTF8.GetBytes("Sample document content"));


            var page = DocumentAnalysisModelFactory.DocumentPage(
                pageNumber: 1,
                width: 8.5f,
                height: 11f,
                unit: DocumentPageLengthUnit.Inch,
                spans: new List<DocumentSpan> { DocumentAnalysisModelFactory.DocumentSpan(0, 23) }
            );

            var mockResult = DocumentAnalysisModelFactory.AnalyzeResult(
               content: "Sample document content",
               pages: new List<DocumentPage> { page },
               tables: new List<DocumentTable>()
           );

            var mockOperation = new Mock<AnalyzeDocumentOperation>();
            mockOperation.Setup(op => op.Value).Returns(mockResult);

            _mockDocClient
                .Setup(client => client.AnalyzeDocumentAsync(WaitUntil.Completed,
                                                             "prebuilt-layout",
                                                             stream,
                                                             It.IsAny<AnalyzeDocumentOptions>(),
                                                             It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockOperation.Object);

            // Act
            var result = await _service.GetDocumentTextAsync(stream);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(0, result[0].Index);
            Assert.Equal(0, result[0].Offset);
            Assert.Equal("Sample document content", result[0].Text.TrimEnd());
        }

        [Fact]
        public void CreateSections_CombinesTextAsNeeded()
        {
            // Arrange
            var pageDetails = new List<PageDetail>
            {
                new PageDetail(0, 0, "This is the text of page 1."),
                new PageDetail(1, 25, "This is the text of page 2.")
            };
            var oid = "test-oid";
            var workspaceId = "test-workspace";
            var blob = "test-blob";

            // Act
            var sections = _service.CreateSections(pageDetails.AsReadOnly(), oid, workspaceId, blob).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.Equal(2, sections.Count);

            Assert.Equal("This is the text of page 1.This is the text of page 2.", sections[0].Content);
            Assert.Equal("test-workspace", sections[0].Workspace);
            Assert.Equal("test-blob#page=1", sections[0].SourcePage);
            Assert.Equal("test-blob", sections[0].SourceFile);

            Assert.Equal("This is the text of page 1.This is the text of page 2.", sections[1].Content);
            Assert.Equal("test-workspace", sections[1].Workspace);
            Assert.Equal("test-blob#page=2", sections[1].SourcePage);
            Assert.Equal("test-blob", sections[1].SourceFile);
        }

        [Fact]
        public void CreateSections_CreatesMultipleSections()
        {
            // Arrange
            var random = new Random();
            string GenerateRandomSentence(int wordCount)
            {
                var words = new List<string>();
                for (int i = 0; i < wordCount; i++)
                {
                    words.Add(new string((char)random.Next('a', 'z' + 1), random.Next(3, 8)));
                }
                return string.Join(" ", words) + ".";
            }

            var longTextPage1 = string.Join(" ", Enumerable.Range(0, 50).Select(_ => GenerateRandomSentence(10)));
            var longTextPage2 = string.Join(" ", Enumerable.Range(0, 60).Select(_ => GenerateRandomSentence(9)));
            var longTextPage3 = string.Join(" ", Enumerable.Range(0, 40).Select(_ => GenerateRandomSentence(8)));

            var pageDetails = new List<PageDetail>
            {
                new PageDetail(0, 0, longTextPage1),
                new PageDetail(1, 0, longTextPage2),
                new PageDetail(2, 0, longTextPage3)
            };

            var oid = "test-oid";
            var workspaceId = "test-workspace";
            var blob = "test-blob";

            // Act
            var sections = _service.CreateSections(pageDetails, oid, workspaceId, blob).ToList();

            // Assert
            Assert.NotNull(sections);
            Assert.True(sections.Count > 1);
            Assert.All(sections, section =>
            {
                Assert.StartsWith(blob, section.Id);
                Assert.Equal(workspaceId, section.Workspace);
                Assert.StartsWith(blob, section.SourceFile);
            });
        }

        /******************************************
        * Helper functions to create mock objects *
        ******************************************/

        private int InvokeFindSplitPoint(string text, int mid, int max = 20_000)
        {
            MethodInfo methodInfo = typeof(DocumentIntelligenceService)
                .GetMethod("FindSplitPoint", BindingFlags.NonPublic | BindingFlags.Instance)!;
            return (int)methodInfo.Invoke(
                _service, new object[] { text, mid, max })!;
        }

        private string InvokeReplaceTablesWithHtml(AnalyzeResult result,
                                                   int pageIndex,
                                                   int[] tableChars,
                                                   List<DocumentTable> tablesOnPage)
        {
            MethodInfo methodInfo = typeof(DocumentIntelligenceService)
                .GetMethod("ReplaceTablesWithHtml", BindingFlags.NonPublic | BindingFlags.Instance)!;
            return (string)methodInfo.Invoke(
                _service, new object[] { result, pageIndex, tableChars, tablesOnPage })!;
        }

        private string GenerateRandomText(Random random, int length, string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 .!?")
        {
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        private List<PageDetail> GenerateRandomPageDetails(Random random, int count)
        {
            var pageDetails = new List<PageDetail>();
            int contentLength = 0;
            for (int i = 0; i < count; i++)
            {
                string text = GenerateRandomText(random, random.Next(100, 25000));
                contentLength += text.Length;
                pageDetails.Add(new PageDetail(i, contentLength, text));
            }
            return pageDetails;
        }
    }
}