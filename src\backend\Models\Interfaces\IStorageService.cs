﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for Blob Storage Service.
    /// </summary>
    public interface IStorageService
    {
        /// <summary>
        /// Gets all workspaces from Blob Storage starting with the prefix.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        Task<List<Workspace>> GetWorkspaces(string prefix, CancellationToken ct);

        /// <summary>
        /// Gets information about a workspace from Blob Storage.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        Task<Workspace> GetWorkspace(string prefix, CancellationToken ct);

        /// <summary>
        /// Creates a new workspace in Blob Storage.
        /// </summary>
        /// <param name="oid">The owner of the workspace</param>
        /// <param name="item">Workspace item</param>
        /// <returns></returns>
        Task<Workspace> CreateWorkspaceAsync(string oid, Workspace item);

        /// <summary>
        /// Updates an existing workspace in Blob Storage.
        /// </summary>
        /// <param name="oid">The owner of the workspace</param>
        /// <param name="item">Workspace item</param>
        /// <returns></returns>
        Task<Workspace> UpdateWorkspaceAsync(string oid, Workspace item);

        /// <summary>
        /// Refreshes the last modified date of an object and it's children in Blob Storage.
        /// </summary>
        /// <param name="prefix"></param>
        /// <returns></returns>
        Task Refresh(string prefix);

        /// <summary>
        /// Deletes an object from Blob Storage.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <returns></returns>
        Task DeleteBlobAsync(string prefix, bool isFile = true);

        /// <summary>
        /// Gets all documents in a workspace from Blob Storage.
        /// </summary>
        /// <param name="prefix">
        /// Specifies a string that filters the results to return 
        /// only blobs whose name begins with the specified prefix</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        Task<List<Document>> GetDocuments(string prefix, CancellationToken ct, bool fetchAll = false);

        /// <summary>
        /// Downloads a document from Blob Storage.
        /// </summary>
        /// <param name="document"></param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        Task<Stream> GetDocumentStreamAsync(string document, CancellationToken ct);

        /// <summary>
        /// Uploads a document to Blob Storage.
        /// </summary>
        /// <param name="oid">The owner of the document</param>
        /// <param name="workspaceId">The workspace the document falls under</param>
        /// <param name="fileName">The name of the document</param>
        /// <param name="documentStream">Stream object holding document content</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        Task<Document> UploadDocumentsAsync(string oid,
                                            string workspaceId,
                                            string fileName,
                                            Stream documentStream,
                                            CancellationToken ct,
                                            string original = ".pdf");
    }
}
