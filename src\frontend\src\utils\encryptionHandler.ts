import { addData, getAllData, deleteData } from "../db/chatDB";
import { Stores } from '../constants/dbConstants';
import { setApiCallFlag } from "../features/apiCallFlagSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import { AppDispatch } from "../store";
import { chatEncryption } from "./chatEncryption";
import { chatMessagesTransform } from "./chatMessagesTransform";
import { decryptionHandler } from "./decryptionHandler";
import { ChatMessageProps } from "./types";
import { addChatArray, updateChatArray } from "../features/AllChatHistorySlice";
import { MAX_CHAT_WINDOW_LENGTH } from "../constants/constants";
import { updateHash } from "../db/chatDB";

export const encryptionHandler = async (chatMessages: ChatMessageProps[], email: string, token: string, dispatch: AppDispatch, currentChatId: string | undefined, currentWorkspaceId: string) => {
    try {
        // Transform messages for encryption
        const transformedMessages = await chatMessagesTransform(chatMessages);
        let workspaceId = currentWorkspaceId;

        // Encrypt the messages
        const encryptedMessage = await chatEncryption(transformedMessages, email, token);

        // Get all current data from the database
        let allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

        // Case 1: New chat (no current chat ID)
        if(!currentChatId && encryptedMessage){
            // Generate encrypted data with a unique ID
            const encryptedData = await transformEncryptedData(encryptedMessage, workspaceId);
            const newData = [{ ...encryptedData }];

            // Add to the database (this will also remove the oldest chat if necessary)
            await addData(Stores.Users, newData);

            // Get updated data from the database
            const newDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

            // Sort by date (most recent first)
            const sortedData = [...newDbData].sort((a, b) =>
                new Date(b.date).getTime() - new Date(a.date).getTime()
            );

            // Find the new chat (should be the most recent)
            const newChat = sortedData.find(data => data.hash === encryptedMessage);

            if (newChat) {
                // Add the new chat to the global state
                dispatch(addChatArray({key: newChat.id, chatArray: chatMessages}));

                // Set the current chat ID
                dispatch(setCurrentChatId(newChat.id));
            }
        }

        // Case 2: Update an existing chat
        else if (allDbData.length > 0 && currentChatId) {
            const chatExists = allDbData.some(data => data.id === currentChatId);

            // Get the workspace ID of the current chat
            for(const elem of allDbData){
                if(elem.id === currentChatId){
                    workspaceId = elem.workspaceId;
                }
            }

            if (chatExists) {
                // Update the hash of the existing chat
                await updateHash(Stores.Users, currentChatId, encryptedMessage ? encryptedMessage : "");

                // Update the chat in the global state
                dispatch(updateChatArray({key: currentChatId, chatArray: chatMessages}));
            }
        }

        // Case 3: Fallback for new chat creation
        else if (encryptedMessage) {
            const encryptedData = transformEncryptedData(encryptedMessage, workspaceId);
            const newData = [{ ...encryptedData }];
            await addData(Stores.Users, newData);
            const newDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

            if(newDbData.length > 0){
                const newChatId = newDbData[newDbData.length-1].id;
                dispatch(addChatArray({key: newChatId, chatArray: chatMessages}));
                dispatch(setCurrentChatId(newChatId));
            }
        }

        // Finish the operation
        dispatch(setApiCallFlag(false));
    } catch (error) {
        console.error('Error in encryptionHandler:', error);
        dispatch(setApiCallFlag(false));
    }
};

function transformEncryptedData(encryptedMessage: string, workspaceId: string) {
    const date = new Date();
    const isoString = date.toISOString();
    const uniqueId = makeAlphanumeric(isoString);

    return {
        hash: encryptedMessage,
        date: isoString,
        id: uniqueId,
        workspaceId: workspaceId
    };
}

export function makeAlphanumeric(dateString: string) {
    return dateString.replace(/[^a-zA-Z0-9]/g, '');
}
