﻿using System.Text.Json.Serialization;

namespace Backend.Models
{
    /// <summary>
    /// Wrapper class for OpenAIPromptExecutionSettings.
    /// </summary>
    public class OpenAISettings
    {
        /// <summary>
        /// Temperature controls the randomness of the completion.
        /// The higher the temperature, the more random the completion.
        /// Default is 1.0.
        /// </summary>
        [JsonPropertyName("temperature")]
        public double? Temperature { get; set; }

        /// <summary>
        /// TopP controls the diversity of the completion.
        /// The higher the TopP, the more diverse the completion.
        /// Default is 1.0.
        /// </summary>
        [JsonPropertyName("top_p")]
        public double? TopP { get; set; }

        /// <summary>
        /// The maximum number of tokens to generate in the completion.
        /// </summary>
        [JsonPropertyName("max_tokens")]
        public int? MaxTokens { get; set; }

        /// <summary>
        /// Number between -2.0 and 2.0. Positive values penalize new tokens
        /// based on whether they appear in the text so far, increasing the
        /// model's likelihood to talk about new topics.
        /// </summary>
        [Json<PERSON>ropertyName("presence_penalty")]
        public double? PresencePenalty { get; set; }

        /// <summary>
        /// Number between -2.0 and 2.0. Positive values penalize new tokens
        /// based on their existing frequency in the text so far, decreasing
        /// the model's likelihood to repeat the same line verbatim.
        /// </summary>
        [JsonPropertyName("frequency_penalty")]
        public double? FrequencyPenalty { get; set; }
    }
}
