import React, { useCallback, useEffect, useMemo, useRef } from "react";
import { useNavigate, useLocation } from "react-router";
import { useDropzone } from "react-dropzone";
import {
  DocumentArrowUp20Filled,
  CheckmarkCircle24Filled,
  Info24Filled,
  DismissCircle24Filled,
} from "@fluentui/react-icons";
import { useAuth } from "../../hooks/useAuth";
import {
  fetchWorkspaceById,
  createWorkspace,
  editWorkspace,
} from "../../services/workspacesService";
import { uploadDocument } from "../../services/documentsService";
import DocumentsList from "../DocsContainer/DocumentsList";
import Tooltip from "../Tooltip/Tooltip";
import { RootState } from "../../store";
import {
  setWorkspace,
  setWorkspaceProperty,
  clearWorkspace,
} from "../../features/workspaceSlice";
import { processResponseStream } from "../../utils/processResponseStream";
import { Workspace, DocumentRecord } from "../../interfaces";
import {
  FILE_TYPE_ACCEPTANCE,
  FILE_MIME_TYPES,
  MAX_NUMBER_OF_DOCUMENTS,
} from "../../constants/constants";
import Toast from "../Modal/ToastModal/Toast";
import useToast from "../../hooks/useToast";
import BottomButtons from "./BottomButtons";
import InputSection from "./InputSection";
import { useAppDispatch, useAppSelector } from "../../app/hooks";

const EditWorkspace: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const { acquireToken, activeAccount } = useAuth();
  // Use typed dispatch hook for better type safety
  const dispatch = useAppDispatch();
  // Use typed selector hook for better type safety
  const { name, description, documents } = useAppSelector(
    (state: RootState) => state.workspace
  );
  const updatedDocumentsRef = useRef<DocumentRecord[]>(documents || []);
  const UploadController = useRef<AbortController | null>(null);
  const FetchController = useRef<AbortController | null>(null);
  const formattedFileTypes = FILE_TYPE_ACCEPTANCE.map((type) =>
    type.replace(/\./g, "").toUpperCase()
  ).join(", ");

  let docLimit = false;
  if (documents) {
    docLimit = documents.length >= MAX_NUMBER_OF_DOCUMENTS ? true : false;
  }
  const workspaceIdFromUrl = useMemo(
    () => new URLSearchParams(location.search).get("id"),
    [location.search]
  );

  const validWorkspaceId = workspaceIdFromUrl || "new-blank-workspace";

  useEffect(() => {
    updatedDocumentsRef.current = documents || [];
  }, [documents]);


  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    dispatch(
      setWorkspaceProperty({ property: name as keyof Workspace, value })
    );
  };

  useEffect(() => {
    const getWorkspaceDetails = async () => {
      const getToken = await acquireToken();
      try {
        if (!workspaceIdFromUrl || !activeAccount) return;

        if (!getToken?.accessToken)
          throw new Error("Token acquisition failed.");

        FetchController.current?.abort();
        UploadController.current?.abort();
        FetchController.current = new AbortController();

        const response = await fetchWorkspaceById(
          workspaceIdFromUrl,
          getToken?.accessToken,
          FetchController.current.signal
        );
        await processResponseStream(response, (data) => {
          if (FetchController.current?.signal.aborted) return;

          if (data.workspace) {
            dispatch(setWorkspace(data.workspace));
          }
        });
      } catch (error) {
        console.error("Error fetching workspace:", error);
      }
    };

    if (workspaceIdFromUrl) {
      getWorkspaceDetails();
    } else {
      dispatch(clearWorkspace());
    }

    return () => {
      dispatch(setWorkspaceProperty({ property: "documents", value: [] }));
    };
  }, [workspaceIdFromUrl, activeAccount, acquireToken, dispatch]);

  const { toasts, triggerToast, closeToast } = useToast();

  const handleSave = useCallback(async () => {
    const getToken = await acquireToken();
    try {
      if (!getToken?.accessToken) throw new Error("Token acquisition failed.");

      if (workspaceIdFromUrl) {
        await editWorkspace(
          workspaceIdFromUrl,
          { name, description },
          getToken?.accessToken
        );
        // setIsUpdated(true);
        triggerToast({
          text: "Workspace information updated",
          icon: <CheckmarkCircle24Filled />,
          duration: 3,
          position: "top-center",
        });
      } else {
        await createWorkspace({ name, description }, getToken?.accessToken);
        navigate("/manage-workspaces");
      }
    } catch (error) {
      console.error("Error saving workspace:", error);
      alert("Failed to save workspace. Please try again.");
    }
  }, [
    acquireToken,
    workspaceIdFromUrl,
    name,
    description,
    navigate,
    triggerToast,
  ]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      // Filter files based on their extensions (happens after the "accept" validation in the onDropzone function)
      const filteredFiles = acceptedFiles.filter((file) => {
        const fileExtension = file.name.split(".").pop()?.toLowerCase();
        return FILE_TYPE_ACCEPTANCE.includes(`.${fileExtension}`);
      });

      // If there are not valid files after filtering, shows the red text error
      if (filteredFiles.length === 0) {
        triggerToast({
          text: `Current file types supported: ${formattedFileTypes}.`,
          icon: <DismissCircle24Filled />,
          duration: 7,
          position: "top-center",
          bgColor: "bg-red-500",
        });
        return;
      }

      // Verifies if the total number of documents exceeds 5
      if (updatedDocumentsRef.current.length + filteredFiles.length > 5) {
        triggerToast({
            text: "You cannot upload more than 5 documents.",
            icon: <DismissCircle24Filled />,
            duration: 7,
            position: "top-center",
            bgColor: "bg-red-500",
        });
        return;
      }

      const getToken = await acquireToken();

      if (!getToken?.accessToken) {
        console.error("Token acquisition failed.");
        return;
      }

      // Start uploading

      const uploadPromises = filteredFiles.map(async (file) => {
        const newDocument: DocumentRecord = {
          name: file.name,
          processed: 0,
        };

        let updatedDocuments = [...updatedDocumentsRef.current, newDocument];
        updatedDocumentsRef.current = updatedDocuments;
        dispatch(
          setWorkspaceProperty({
            property: "documents",
            value: updatedDocuments,
          })
        );

        UploadController.current = new AbortController();

        try {
          if (UploadController.current) {
            // Triggers Toast notification for documents availability when the file gets listed on the docs list
            triggerToast({
              text: "Newly updated documents may take a minute or two before they become available.",
              icon: <Info24Filled />,
              duration: 7,
              position: "top-center",
            });

            const response = await uploadDocument(
              validWorkspaceId,
              file,
              getToken?.accessToken,
              UploadController.current.signal
            );
            await processResponseStream(response, (data) => {
              updatedDocuments = [...updatedDocumentsRef.current];

              if (data.documents) {
                data.documents.forEach((newDoc: DocumentRecord) => {
                  const existingDocIndex = updatedDocuments.findIndex(
                    (doc) => doc.name === newDoc.name
                  );
                  if (existingDocIndex !== -1) {
                    updatedDocuments = updatedDocuments.map((doc, index) =>
                      index === existingDocIndex
                        ? {
                            ...doc,
                            processed: newDoc.processed,
                            expires: newDoc.expires,
                          }
                        : doc
                    );
                  } else {
                    updatedDocuments = [...updatedDocuments, newDoc];
                  }
                });
              }
              updatedDocumentsRef.current = updatedDocuments;
              dispatch(
                setWorkspaceProperty({
                  property: "documents",
                  value: updatedDocuments,
                })
              );
            });
          } else {
            throw new Error("UploadController is null");
          }
        } catch (error) {
          let errorMessage = `There was an error uploading document ${file.name}.`;

          if (error instanceof Error) {
            try {
              const errorJson = JSON.parse(error.message);
              errorMessage = errorJson.Message;
            } catch (e) {}
          }

          updatedDocuments = updatedDocumentsRef.current.filter(
            (doc) => doc.name !== file.name
          );
          updatedDocumentsRef.current = updatedDocuments;
          dispatch(
            setWorkspaceProperty({
              property: "documents",
              value: updatedDocuments,
            })
          );

          console.error(`Error uploading document ${file.name}.`, error);
          alert(errorMessage);
        }
      });

      // Wait for all uploads to complete
      await Promise.all(uploadPromises);

      // Resets the file input value
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

    },
    [acquireToken, validWorkspaceId, documents, dispatch, triggerToast]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: FILE_MIME_TYPES,
  });

  return (
    <div className="relative flex flex-col p-8 items-center h-full w-full bg-white dark:bg-zinc-800">
      <h1 className="text-2xl font-bold mb-6 dark:text-gray-300">
        Document Workspaces
      </h1>
      <div className="w-full max-w-2xl">
        <p>
          Add documents of type PDF, Word or Powerpoint below. A <a href="https://ajg0.sharepoint.com/teams/GO-ai_at_Gallagher/Shared%20Documents/Forms/AllItems.aspx?id=%2Fteams%2FGO-ai_at_Gallagher%2FShared+Documents%2FGallagher+AI+Document+Upload+Quick+Start+Guide.pdf&parent=%2Fteams%2FGO-ai_at_Gallagher%2FShared+Documents" target="_blank" className="text-gallagher-dark-300 dark:text-sky-400 underline underline-offset-6 decoration-gallagher-dark-300"> Quick Start guide </a>
          is available should you require assistance.
        </p>
        {workspaceIdFromUrl && activeAccount && (
          <div>
            <div className="flex justify-between w-full my-5">
              <label className="text-xl font-bold dark:text-gray-300">
                Documents uploaded:
              </label>
            </div>
            {!docLimit && (
              <Tooltip
                message={`Accepts up to ${MAX_NUMBER_OF_DOCUMENTS} documents`}
              >
                <div
                  {...getRootProps()}
                  className={`w-full h-10 flex items-center justify-center border-2 border-dashed p-10 rounded cursor-pointer ${
                    isDragActive
                      ? "bg-gallagher-dark-300 dark:bg-gallagher-dark-300 text-white"
                      : "bg-white hover:bg-gallagher-dark-300 hover:text-white"
                  } hover:shadow-md hover:border-white dark:text-gray-300 dark:hover:bg-gallagher-dark-300 dark:bg-zinc-800 dark:border-zinc-600`}
                >
                  <input {...getInputProps()} />
                  <DocumentArrowUp20Filled
                    className={`mr-2 group-hover:text-white${
                      isDragActive
                        ? "text-white"
                        : "text-gallagher-dark-100 group-hover:text-white"
                    } dark:text-gray-300 dark:group-hover:text-white`}
                  />
                  <p className={isDragActive ? "text-white" : ""}>
                    {isDragActive
                      ? "Drop the documents here..."
                      : "Drag and drop a file or click"}
                  </p>
                </div>
              </Tooltip>
            )}

            <DocumentsList />
          </div>
        )}
        <InputSection workspaceIdFromUrl={workspaceIdFromUrl} name={name} description={description} inputChange={handleInputChange}/>
        <BottomButtons workspaceIdFromUrl={workspaceIdFromUrl} activeAccount={activeAccount} save={handleSave}/>
      </div>
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          id={toast.id}
          position={toast.position}
          text={toast.text}
          icon={toast.icon}
          duration={toast.duration}
          onClose={() => closeToast(toast.id)}
          bgColor={toast.bgColor}
        />
      ))}
    </div>
  );
};

export default EditWorkspace;
