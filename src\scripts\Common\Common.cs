﻿using Azure.Core;
using Azure.Identity;
using Azure.Search.Documents;
using Azure.Storage.Blobs;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Common
{
    public class Common
    {
        public static Dictionary<string, SearchClient>? _searchClients { get; set; }
        public static Dictionary<string, BlobContainerClient>? _blobClients { get; set; }
        public static DateTimeOffset _thresholdDate { get; set; }
        public static ILogger<Common>? _logger { get; set; }

        private static IConfiguration? _config { get; set; }
        private static TokenCredential? _credential { get; set; }

        private static InMemoryChannel _channel = new InMemoryChannel();

        /// <summary>
        /// Initializes the Common class with the necessary clients and logging based on the provided options.
        /// </summary>
        /// <param name="options">The options to specify which services to initialize. If null, all services will be initialized.</param>
        public static void Initialize(InitializationOptions? options = null)
        {
            options ??= new InitializationOptions(); // Use default options if none are provided

            _config = LoadConfiguration();
            _credential = GetCredential();

            if (options.InitializeSearchClients)
            {
                InitializeSearchClient();
            }

            if (options.InitializeBlobClients)
            {
                InitializeBlobClient();
            }

            if (options.InitializeLogger)
            {
                InitializeLogger();
            }

            if (options.InitializeThresholdDate)
            {
                InitializeThresholdDate();
            }
        }

        private static IConfiguration LoadConfiguration()
        {
            bool isLocal = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Local";
            var env = isLocal ? "US-DEV" : Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";

            return new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{env}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables()
                .Build();
        }

        private static TokenCredential GetCredential()
        {
            bool isLocal = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Local";
            return isLocal ? new AzureCliCredential() : new ManagedIdentityCredential();
        }

        private static void InitializeSearchClient()
        {
            ArgumentNullException.ThrowIfNullOrEmpty(_config!["AzureSearch:Endpoints:Default"]);
            ArgumentNullException.ThrowIfNullOrEmpty(_config["AzureSearch:Index"]);

            _searchClients = new();
            foreach (var endpoint in _config.GetSection("AzureSearch:Endpoints").Get<Dictionary<string, string>>()!)
            {
                _searchClients.Add(endpoint.Key,
                    new SearchClient(
                        new Uri(endpoint.Value),
                        _config["AzureSearch:Index"]!, _credential));
            }
        }

        private static void InitializeBlobClient()
        {
            ArgumentNullException.ThrowIfNullOrEmpty(_config!["AzureStorage:Endpoints:Default"]);
            ArgumentNullException.ThrowIfNullOrEmpty(_config["AzureStorage:Container"]);

            Dictionary<string, BlobServiceClient> clients = new();
            foreach (var endpoint in _config.GetSection("AzureStorage:Endpoints").Get<Dictionary<string, string>>()!)
            {
                clients.Add(endpoint.Key,
                    new BlobServiceClient(
                        new Uri(endpoint.Value), _credential));
            }

            _blobClients = clients
                .ToDictionary(
                    kvp => kvp.Key,
                    kvp => kvp.Value.GetBlobContainerClient(_config["AzureStorage:Container"]));
        }

        private static void InitializeThresholdDate()
        {
            ArgumentNullException.ThrowIfNullOrEmpty(_config!["AzureStorage:ExpirationPolicy"]);
            _thresholdDate = DateTimeOffset.UtcNow.AddDays(-_config.GetValue<int>("AzureStorage:ExpirationPolicy"));
        }

        private static void InitializeLogger()
        {
            ArgumentNullException.ThrowIfNullOrEmpty(_config!["ApplicationInsights:ConnectionString"]);

            IServiceCollection services = new ServiceCollection();
            services.Configure<TelemetryConfiguration>(telemetryConfig => telemetryConfig.TelemetryChannel = _channel);
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.AddApplicationInsights(
                    configureTelemetryConfiguration: (tConfig) => tConfig.ConnectionString = _config["ApplicationInsights:ConnectionString"],
                    configureApplicationInsightsLoggerOptions: (options) => { }
                );
            });

            IServiceProvider serviceProvider = services.BuildServiceProvider();
            _logger = serviceProvider.GetRequiredService<ILogger<Common>>();
        }

        /// <summary>
        /// Logs a message to the Application Insights instance and flushes the channel.
        /// </summary>
        /// <param name="logLevel"></param>
        /// <param name="message"></param>
        /// <param name="ex"></param>
        /// <exception cref="ArgumentOutOfRangeException"></exception>
        public static void Log(LogLevel logLevel, string message, Exception? ex = null)
        {
            switch (logLevel)
            {
                case LogLevel.Information:
                    _logger!.LogInformation(message);
                    break;
                case LogLevel.Warning:
                    _logger!.LogWarning(ex, message);
                    break;
                case LogLevel.Error:
                    _logger!.LogError(ex, message);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(logLevel), logLevel, null);
            }
            _channel.Flush();
        }
    }
}

