import { TEMPERATURE, TOP_P, FREQUENCY_PENALTY, PRESENCE_PENALTY } from "../constants/SettingsTabConstants";
import { USER_SETTINGS_ID, version as initialVersion, Stores } from "../constants/dbConstants";
import { UserSettingsTypes } from "../interfaces";

// Variable dedicated for this specific connection
let settingsDB: IDBDatabase | null = null;

export const initUserSettingDB = async (): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    // Close any existing connection
    if (settingsDB) {
      settingsDB.close();
      settingsDB = null;
    }

    // First, open without version to get current version
    const versionRequest = indexedDB.open('Settings');
    
    versionRequest.onsuccess = () => {
      const currentVersion = versionRequest.result.version;
      versionRequest.result.close();
      
      // Now open with the correct version
      openDatabaseWithVersion(currentVersion, resolve, reject);
    };
    
    versionRequest.onerror = () => {
      // If we can't even open without version, try with default
      // console.log("Error getting current database version, using default");
      openDatabaseWithVersion(initialVersion, resolve, reject);
    };
  });
};

const openDatabaseWithVersion = (version: number, resolve: (value: boolean) => void, reject: (reason: any) => void) => {
  // console.log(`Opening Settings database with version ${version}`);
  const request = indexedDB.open('Settings', version);
  
  request.onupgradeneeded = () => {
    // console.log(`Database upgrade from v${event.oldVersion} to v${event.newVersion}`);
    const db = request.result;
    
    // Create stores as needed
    if (!db.objectStoreNames.contains(Stores.UserSettings)) {
      // console.log('Creating UserSettings store');
      db.createObjectStore(Stores.UserSettings, { keyPath: 'id' });
    }
    
    // Create other stores if needed
    if (!db.objectStoreNames.contains(Stores.LanguageSettings)) {
      // console.log('Creating LanguageSettings store');
      db.createObjectStore(Stores.LanguageSettings, { keyPath: 'id' });
    }
  };
  
  request.onsuccess = async () => {
    settingsDB = request.result;
    
    // Check if stores exist
    const storeExists = Array.from(settingsDB.objectStoreNames).includes(Stores.UserSettings);
    // console.log('Store UserSettings exists:', storeExists);
    
    if (!storeExists && version === request.result.version) {
      // Store doesn't exist and we're at the current version - need to upgrade
      settingsDB.close();
      settingsDB = null;
      
      // console.log(`Store UserSettings missing, incrementing version from ${version} to ${version + 1}`);
      // Try to open with a higher version
      openDatabaseWithVersion(version + 1, resolve, reject);
      return;
    }
    
    resolve(true);
  };
  
  request.onerror = () => {
    console.error('Error opening settings database:', request.error);
    reject(false);
  };
};

export const getUserSetting = async (id: string): Promise<UserSettingsTypes> => {
  // Make sure we have a valid connection
  if (!settingsDB) {
    const initialized = await initUserSettingDB();
    if (!initialized) {
      // If initialization failed, try once more
      await initUserSettingDB();
    }
  }
  
  // Check if the store exists before trying to access it
  if (!settingsDB || !Array.from(settingsDB.objectStoreNames).includes(Stores.UserSettings)) {
    console.error('UserSettings store not available');
    // Return default settings if we can't access the store
    return {
      id: USER_SETTINGS_ID,
      temperature: TEMPERATURE.defaultValue,
      topP: TOP_P.defaultValue,
      frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
      presencePenalty: PRESENCE_PENALTY.defaultValue,
    };
  }

  return new Promise((resolve) => {
    try {
      const transaction = settingsDB!.transaction([Stores.UserSettings], 'readonly');
      const store = transaction.objectStore(Stores.UserSettings);
      const request = store.get(id);

      request.onsuccess = () => {
        const defaultSettings = {
          id: USER_SETTINGS_ID,
          temperature: TEMPERATURE.defaultValue,
          topP: TOP_P.defaultValue,
          frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
          presencePenalty: PRESENCE_PENALTY.defaultValue,
        };

        const savedSettings = request.result;
        if (!savedSettings) {
          resolve(defaultSettings);
          return;
        }

        const isValid = (val: number, config: { minValue: number; maxValue: number }) =>
          typeof val === "number" && val >= config.minValue && val <= config.maxValue;

        if (
          !isValid(savedSettings.temperature, TEMPERATURE) ||
          !isValid(savedSettings.topP, TOP_P) ||
          !isValid(savedSettings.frequencyPenalty, FREQUENCY_PENALTY) ||
          !isValid(savedSettings.presencePenalty, PRESENCE_PENALTY)
        ) {
          // If there's no value on settings object, it loads the default values
          saveUserSetting(defaultSettings);
          resolve(defaultSettings);
          return;
        }

        const mergedSettings = { ...defaultSettings, ...savedSettings };

        resolve(mergedSettings);
      };

      request.onerror = () => {
        console.error('Error getting setting:', request.error);
        // Return default values in case of error
        resolve({
          id: USER_SETTINGS_ID,
          temperature: TEMPERATURE.defaultValue,
          topP: TOP_P.defaultValue,
          frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
          presencePenalty: PRESENCE_PENALTY.defaultValue,
        });
      };
    } catch (error) {
      console.error('Exception in getUserSetting:', error);
      // Return default values in case of exception
      resolve({
        id: USER_SETTINGS_ID,
        temperature: TEMPERATURE.defaultValue,
        topP: TOP_P.defaultValue,
        frequencyPenalty: FREQUENCY_PENALTY.defaultValue,
        presencePenalty: PRESENCE_PENALTY.defaultValue,
      });
    }
  });
};

export const saveUserSetting = async (settings: UserSettingsTypes): Promise<boolean> => {
  // Make sure we have a valid connection
  if (!settingsDB) {
    const initialized = await initUserSettingDB();
    if (!initialized) {
      // If initialization failed, try once more
      await initUserSettingDB();
    }
  }
  
  // Check if the store exists before trying to access it
  if (!settingsDB || !Array.from(settingsDB.objectStoreNames).includes(Stores.UserSettings)) {
    console.error('UserSettings store not available for saving');
    return false;
  }

  return new Promise((resolve) => {
    try {
      const transaction = settingsDB!.transaction([Stores.UserSettings], 'readwrite');
      const store = transaction.objectStore(Stores.UserSettings);

      // No need to delete first, put will overwrite if it exists
      const request = store.put(settings);

      request.onsuccess = () => {
        // console.log('User setting saved successfully');
        resolve(true);
      };

      request.onerror = () => {
        console.error('Error saving setting:', request.error);
        resolve(false);
      };
      
      transaction.oncomplete = () => {
        // console.log('Transaction completed successfully');
      };
      
      transaction.onerror = () => {
        console.error('Transaction error:', transaction.error);
      };
    } catch (error) {
      console.error('Exception in saveUserSetting:', error);
      resolve(false);
    }
  });
};