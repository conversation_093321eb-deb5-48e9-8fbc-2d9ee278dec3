﻿#pragma warning disable SKEXP0001

using Azure.Search.Documents;
using Backend.Models;
using Backend.Services;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.SemanticKernel;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureSearchService(this IServiceCollection services, IConfiguration config)
        {
            Dictionary<string, SearchClient> clients = new();
            foreach (var endpoint in config.GetSection("AzureSearch:Endpoints").Get<Dictionary<string, string>>()!)
            {
                clients.Add(endpoint.Key,
                    new SearchClient(
                        new Uri(endpoint.Value),
                        config["AzureSearch:Index"]!, _azureCredential));
            }

            services.AddSingleton<ISearchService, SearchService>(sp =>
            {
                return new SearchService(
                    clients,
                    sp.GetRequiredService<Kernel>().GetRequiredService<ITextEmbeddingGenerationService>(),
                    sp.GetRequiredService<ILogger<SearchService>>(),
                    config.GetValue<int>("DefaultSettings:MinimumContextLength"),
                    config.GetValue<int>("DefaultSettings:Top"));
            });
        }
    }
}
