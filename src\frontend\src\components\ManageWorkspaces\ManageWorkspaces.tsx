import React, { useState, useEffect } from "react";
import Styles from "./ManageWorkspaces.module.css";
import {
  DocumentArrowUp20Filled,
  Delete24Filled,
  Folder<PERSON>dd24R<PERSON>ular,
  FolderPerson28Filled,
  DismissCircle24Filled,
} from "@fluentui/react-icons";
import { useNavigate } from "react-router";
import useFetchWorkspaces from "../../hooks/useFetchWorkspaces";
import useDeleteWorkspace from "../../hooks/useDeleteWorkspace";
import { MAX_NUMBER_OF_MANAGE_WORKSPACE } from "../../constants/constants";
import Tooltip from "../Tooltip/Tooltip";
import DotsAnimation from "../LoadAnimation/DotsAnimation";
import { CheckmarkCircle24Filled } from "@fluentui/react-icons";
import Toast from "../Modal/ToastModal/Toast";
import useToast from "../../hooks/useToast";
import ConfirmToast from "../Modal/ToastModal/ConfirmToast";
import useApiWithAuth from "../../hooks/useApiWithAuth";

const ManageWorkspaces: React.FC = () => {
  const navigate = useNavigate();
  const { refreshToken } = useApiWithAuth();
  const { workspaces, loading, error, setWorkspaces, refetch } = useFetchWorkspaces();
  const { handleDelete, deleteError, deletingWorkspaceId } = useDeleteWorkspace(setWorkspaces);
  const { toasts, triggerToast, closeToast } = useToast();
  const [confirmId, setConfirmId] = useState<string | null>(null);
  const limitFlag = workspaces.length === MAX_NUMBER_OF_MANAGE_WORKSPACE;

  // Effect to refresh token when component mounts
  useEffect(() => {
    const initAuth = async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error("Error refreshing token:", error);
      }
    };

    initAuth();
  }, []);

  const handleEdit = (id: string) => {
    navigate(`/edit-workspace?id=${id}`);
  };

  const handleCreateNew = () => {
    navigate("/edit-workspace");
  };

  return (
    <div className="relative flex flex-col p-8 items-center h-full w-full bg-white dark:bg-zinc-800 dark:text-white">
      <h1 className="text-2xl font-bold mb-6">Document Workspaces</h1>
      <div className="w-full h-full max-w-2xl">
        <p>
          Add documents to workspaces below. You can then chat against these documents from the main chat page. A
          <a href="https://ajg0.sharepoint.com/teams/GO-ai_at_Gallagher/Shared%20Documents/Forms/AllItems.aspx?id=%2Fteams%2FGO-ai_at_Gallagher%2FShared+Documents%2FGallagher+AI+Document+Upload+Quick+Start+Guide.pdf&parent=%2Fteams%2FGO-ai_at_Gallagher%2FShared+Documents" target="_blank" className="text-gallagher-dark-300 dark:text-sky-400 underline underline-offset-6 decoration-gallagher-dark-300"> Quick Start guide </a>
          is available should you require assistance.
        </p>
        <div className="flex justify-between items-center mb-4 mt-5">
          <h2 className="text-xl font-semibold">My Workspaces:</h2>
          <Tooltip message={`You can have maximum ${MAX_NUMBER_OF_MANAGE_WORKSPACE} workspaces`}>
            <button
              className={`group cursor-pointer flex items-center p-2 rounded ${
                limitFlag
                  ? "bg-slate-200 dark:bg-zinc-800 cursor-not-allowed"
                  : "bg-white dark:bg-zinc-700 hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 hover:shadow-md border-2 dark:border-zinc-600 hover:border-gallagher-dark-300"
              }`}
              onClick={handleCreateNew}
              disabled={limitFlag}
              aria-label="Create New Workspace"
              title="Create New Workspace"
            >
              <FolderAdd24Regular
                className={`mr-2 text-gallagher-dark-100 dark:text-gray-300 ${
                  limitFlag ? "" : "group-hover:text-white"
                }`}
              />
              <span className="text-black dark:text-gray-300 group-hover:text-white dark:group-hover:text-white">
                Create new workspace
              </span>
            </button>
          </Tooltip>
        </div>
        <div className="space-y-2">
          {loading ? (
            <div className="relative flex flex-col items-center">
              Loading
              <DotsAnimation />
            </div>
          ) : error ? (
            <p className="text-red-500">{error}</p>
          ) : workspaces.length > 0 ? (
            workspaces.map((workspace) => (
              <div
                key={workspace.id}
                className="flex items-center bg-gallagher-blue-50 dark:bg-zinc-700 rounded-xl justify-between gap-xs-2"
              >
                <div
                  className="group flex items-center hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500 cursor-pointer px-6 py-8 w-[95%] justify-between hover:rounded-xl"
                  onClick={() => handleEdit(workspace.id)}
                  aria-label="Edit Workspace"
                  title="Edit Workspace"
                >
                  <div className="flex items-center w-[100%]">
                    <FolderPerson28Filled className="mr-4 text-xl text-gallagher-dark-100 dark:text-gray-300 dark:group-hover:text-white" />
                    <div className="flex w-full flex-col pr-2">
                      <p className="font-bold group-hover:text-gallagher-dark-200 dark:group-hover:text-white break-all">
                        {workspace.name}
                      </p>
                      <p className="group-hover:text-gallagher-dark-200 dark:group-hover:text-white break-all">
                        {workspace.description}
                      </p>
                    </div>
                    <DocumentArrowUp20Filled className="text-xl text-gallagher-dark-100 dark:text-gray-300 w-6 h-6 dark:group-hover:text-white" />
                  </div>
                </div>
                <div
                  className={`group flex items-center hover:bg-gallagher-blue-100 dark:hover:bg-gallagher-blue-500 overflow-hidden h-full group-hover:bg-gallagher-blue-100 cursor-pointer hover:rounded-xl ${
                    Styles.deleteSection
                  } ${
                    deletingWorkspaceId === workspace.id
                      ? "pointer-events-none"
                      : ""
                  }`}
                  title="Delete Workspace"
                  onClick={async (e) => { e.stopPropagation();
                    setConfirmId(workspace.id);
                      }}
                >
                  {deletingWorkspaceId === workspace.id ? (
                    <span className="text-gray-400 group-hover:text-gallagher-dark-200 dark:hover:text-white">
                      Deleting...
                    </span>
                  ) : (
                    <Delete24Filled className="text-gallagher-dark-100 dark:text-gray-300" />
                  )}
                </div>
              </div>
            ))
          ) : (
            <p>No workspaces found</p>
          )}
        </div>
        {deleteError && <p className="text-red-500">{deleteError}</p>}
        <button
          className="cursor-pointer bg-white dark:bg-zinc-700 hover:bg-gallagher-dark-300 dark:hover:bg-gallagher-dark-300 dark:text-gray-300 hover:text-white hover:shadow-md border-2 dark:border-zinc-600 hover:border-gallagher-dark-300 p-2 px-4 rounded-sm box-border self-start mt-4"
          onClick={() => navigate("/")}
          aria-label="Back to Chats"
          title="Back to Chats"
        >
          Close
        </button>
      </div>
      {toasts.map((toast) => (
        <Toast
          key={toast.id}
          id={toast.id}
          position={toast.position}
          text={toast.text}
          icon={toast.icon}
          duration={toast.duration}
          onClose={() => closeToast(toast.id)}
          bgColor={toast.bgColor}
        />
      ))}
      {confirmId && (
        <ConfirmToast
          text = "Are you sure you want to delete this workspace? Any chats related to this workspace will be deleted too."
          position = "top-center"
          onCancel={() => setConfirmId(null)}
          onConfirm={async () => {
            try {
              await handleDelete(confirmId);

              // Immediately refetch workspaces to update the UI
              refetch();

              setConfirmId(null);
              triggerToast({
                text: "Workspace deleted successfully",
                icon: <CheckmarkCircle24Filled />,
                duration: 2,
                position: "top-center",
              });
            } catch (error) {
              console.error("Error in confirm delete handler:", error);
              triggerToast({
                text: "Failed to delete workspace. Please try again.",
                icon: <DismissCircle24Filled />,
                duration: 3,
                position: "top-center",
                bgColor: "bg-red-500"
              });
            }
          }}
        />
      )}
    </div>
  );
};

export default ManageWorkspaces;