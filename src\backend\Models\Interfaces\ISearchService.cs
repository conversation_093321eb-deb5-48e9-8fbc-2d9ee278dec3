﻿using Azure.Search.Documents;

namespace Backend.Models
{
    /// <summary>
    /// Interface for Search Service
    /// </summary>
    public interface ISearchService
    {
        /// <summary>
        /// Queries the search index for information.
        /// </summary>
        /// <param name="workspace">Workspace</param>
        /// <param name="settings">Search Settings</param>
        /// <param name="textQuery">Textual query to be used for text based search</param>
        /// <param name="vector">Vector query to be used for the vector based search</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        Task<SectionItem[]> QueryDocumentsAsync(
            string workspace,
            SearchSettings settings,
            string? textQuery = null,
            float[]? vector = null,
            CancellationToken ct = default);

        /// <summary>
        /// Gets the search client.
        /// </summary>
        /// <param name="val"></param>
        /// <returns></returns>
        SearchClient GetSearchClient(string val);

        /// <summary>
        /// Adds sections to the search index.
        /// </summary>
        /// <param name="sections">Sections to add</param>
        /// <returns></returns>
        Task AddToSearchAsync(IEnumerable<Section> sections, string PDL = "Default");
    }
}
