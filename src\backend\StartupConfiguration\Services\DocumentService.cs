﻿using Azure.AI.FormRecognizer.DocumentAnalysis;
using Backend.Models;
using Backend.Services;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureDocumentIntelligenceService(this IServiceCollection services, IConfiguration config)
        {
            services.AddSingleton<IDocumentIntelligenceService, DocumentIntelligenceService>(sp =>
            {
                return new DocumentIntelligenceService(
                    new DocumentAnalysisClient(new Uri(config["AzureDocIntel:Endpoint"]!), _azureCredential),
                    sp.GetRequiredService<ILogger<DocumentIntelligenceService>>(),
                    config.GetValue<int>("ChunkSettings:SectionOverlap"));
            });
        }
    }
}
