﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for OpenAI Service
    /// </summary>
    public interface IOpenAIService
    {
        /// <summary>
        /// Prompts the AI and processes the response
        /// </summary>
        /// <param name="request"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        IAsyncEnumerable<Content> PromptAI(Chat request, CancellationToken ct, IEnumerable<SectionItem>? searchContent = null);

        /// <summary>
        /// Prompt AI about documentation in the request workspace
        /// </summary>
        /// <param name="request"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        IAsyncEnumerable<Content> PromptAIAboutDocumentation(Chat request, CancellationToken ct);
    }
}
