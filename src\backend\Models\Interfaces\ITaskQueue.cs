﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for Task Queue
    /// </summary>
    public interface ITaskQueue
    {
        /// <summary>
        /// Queues a task to be processed by a worker, assigned round robin style.
        /// </summary>
        /// <param name="taskItem">TaskItem object containing the details of the task</param>
        /// <param name="timestamp">Optional parameter to set the time in which to process the task</param>
        /// <returns></returns>
        ValueTask QueueItemAsync(TaskItem taskItem, DateTime? timestamp = null);

        /// <summary>
        /// Deserializes a task file and returns the task details.
        /// </summary>
        /// <param name="workerId">WorkerId</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        Task<TaskItem> FetchTasksAsync(int workerId, CancellationToken ct);

        /// <summary>
        /// Deletes a task file.
        /// </summary>
        /// <param name="filePath">Path to the task file</param>
        void DeleteTaskFile(string filePath);
    }
}
