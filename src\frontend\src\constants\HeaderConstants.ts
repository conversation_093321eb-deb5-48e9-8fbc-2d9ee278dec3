import {
  Chat24Regular,
  Folder24Regular,
  QuestionCircle24Regular,
  CalendarAgenda24Regular,
  Megaphone24Regular,
  Settings24Regular,
  Translate24Regular,
} from "@fluentui/react-icons";

//for header menu - desktop
export const optionsToShow = [
  { label: "Chat", href: "/", icon: Chat24Regular, parallelRoutes: [] },
  {
    label: "Document Workspaces",
    href: "/manage-workspaces",
    icon: Folder24Regular,
    parallelRoutes: ["/edit-workspace"],
  },
  {
    label: "Updates",
    href: "/updates",
    icon: Megaphone24Regular,
    parallelRoutes: [],
  },
];

//for dropdown menu - desktop
export const optionsForDropdown = [
  {
    label: "Translation",
    href: "/doc-translation",
    icon: Translate24Regular,
    parallelRoutes: [],
  },
  {
    label: "Settings",
    href: "/settings",
    icon: Settings24Regular,
    parallelRoutes: [],
  },
  {
    label: "Disclaimer",
    href: "/disclaimer",
    icon: CalendarAgenda24Regular,
    parallelRoutes: [],
  },
  {
    label: "Help",
    href: "/help",
    icon: QuestionCircle24Regular,
    parallelRoutes: [],
  },
];

//for hamburger menu - mobile
export const options = [
  { label: "Chat", href: "/", icon: Chat24Regular, parallelRoutes: [] },
  {
    label: "Document Workspaces",
    href: "/manage-workspaces",
    icon: Folder24Regular,
    parallelRoutes: ["/edit-workspace"],
  },
  {
    label: "Updates",
    href: "/updates",
    icon: Megaphone24Regular,
    parallelRoutes: [],
  },
  {
    label: "Translation",
    href: "/doc-translation",
    icon: Translate24Regular,
    parallelRoutes: ["/text-translation"],
 
  },
  {
    label: "Settings",
    href: "/settings",
    icon: Settings24Regular,
    parallelRoutes: [],
  },
  {
    label: "Disclaimer",
    href: "/disclaimer",
    icon: CalendarAgenda24Regular,
    parallelRoutes: [],
  },
  {
    label: "Help",
    href: "/help",
    icon: QuestionCircle24Regular,
    parallelRoutes: [],
  },
];

export const MAX_RESOLUTION_FOR_MOBILE = 1024;

export const MAX_USER_NAME_LENGTH = 20;
