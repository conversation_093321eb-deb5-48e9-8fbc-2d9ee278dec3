using System.Text;
using System.Text.Json.Nodes;
using Xunit;

namespace BackendIntegrationTests
{
    [CollectionDefinition("Chat Tests")]
    public class ChatTestsCollection : ICollectionFixture<BackendIntegrationTests> { }

    [Collection("Chat Tests")]
    public class ChatControllerTests
    {
        private readonly BackendIntegrationTests _fixture;

        public ChatControllerTests(BackendIntegrationTests fixture)
        {
            _fixture = fixture;
        }

        [Fact]
        public async Task STREAM_Chat_ReturnsOkResult()
        {
            // Arrange
            var jsonObject = new JsonObject
            {
                ["settings"] = _fixture.Settings,
                ["messages"] = GenerateMessage("user", "text", 
                "Is it possible there is a planet 9 or X? Is there any evidence for such a planet?")
            };

            var content = new StringContent(jsonObject.ToString(), Encoding.UTF8, "application/json");

            // Act
            var response = await _fixture.Client.PostAsync("/Chat", content);

            // Assert
            response.EnsureSuccessStatusCode(); // HTTP Status Code 200-299
            var responseString = await response.Content.ReadAsStringAsync();
            Assert.Contains("planet", responseString.ToLower());
        }

        /******************************************
         *   Helper functions to create objects
         ******************************************/

        private JsonArray GenerateMessage(string role, string type, string value)
        {
            var content = new JsonObject
            {
                ["type"] = type,
                ["value"] = value
            };
            var message = new JsonObject
            {
                ["role"] = role,
                ["content"] = new JsonArray { content }
            };
            return new JsonArray { message };
        }
    }
}
