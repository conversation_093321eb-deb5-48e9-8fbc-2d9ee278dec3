export const TEMPERATURE = {
    defaultValue: 0.01,
    minValue:0.0,
    maxValue: 1.0,
    step:0.01,
    label: "Creativity (Temperature)",
    description: "Controls randomness. Lowering the temperature means that the model will produce more repetitive and deterministic responses. Increasing the temperature will result in more unexpected or creative responses. *Try not to adjust this while at the same time adjusting Top P. Try one or the other.*",
    minHint: "Predictable",
    maxHint: "Creative",
}

export const TOP_P = {
    defaultValue: 0.95,
    minValue:0.0,
    maxValue: 1.0,
    step:0.01,
    label: "Top word choices (Top P)",
    description: "Another way to control creativity, but works differently to Temperature. Instead of considering all possible words, it limits the choices to a top percentage based on probability. So, if you set Top P to 0.9, you’re only picking from the top 90% of the most likely words. It's like choosing from the best of the best, while ignoring the less likely ones.",
    minHint: "Less common",
    maxHint: "More likely",
}

export const FREQUENCY_PENALTY = {
    defaultValue: 0.0,
    minValue:0.0,
    maxValue: 2.0,
    step:0.01,
    label: "Frequency of word repetition (Frequency Penalty)",
    description: "Tell the model not to repeat a word that has already been used multiple times in the conversation. The smaller the value, the less chance of repeated words from the AI.",
    minHint: "Less repeated words",
    maxHint: "More repeated words",
}

export const PRESENCE_PENALTY = {
    defaultValue: 0.0,
    minValue:0.0,
    maxValue: 2.0,
    step:0.01,
    label: "Repeated Words (Presence Penalty)",
    description: "Prevents the model from repeating a word, even if it's only been used once.",
    minHint: "Prevent repeated words",
    maxHint: "Allow repeated words",
}