using Azure.Search.Documents.Models;
using Azure.Search.Documents;
using Azure.Storage.Blobs;
using Azure;
using Backend.Models;
using Backend.Models.Context;

public class TaskProcessingService : BackgroundService
{
    private readonly ITaskQueue _taskQueue;
    private readonly ILogger<TaskProcessingService> _logger;
    private readonly Dictionary<string, BlobContainerClient> _containerClients;
    private readonly IDocumentIntelligenceService _docService;
    private readonly ISearchService _searchService;
    private readonly int _workerCount;
    private readonly int _retryLimit;

    public TaskProcessingService(ITaskQueue taskQueue,
                                 ILogger<TaskProcessingService> logger,
                                 Dictionary<string, BlobContainerClient> containerClients,
                                 IDocumentIntelligenceService docService,
                                 ISearchService searchService,
                                 int workerCount = 3,
                                 int retryLimit = 5)
    {
        _taskQueue = taskQueue;
        _logger = logger;
        _containerClients = containerClients;
        _docService = docService;
        _searchService = searchService;
        _workerCount = workerCount;
        _retryLimit = retryLimit;
    }

    protected BlobContainerClient GetContainerClient(string PDL)
    {
        return _containerClients.TryGetValue(
            PDL, out var client) ? client : _containerClients["Default"];
    }

    /// <summary>
    /// Starts the Task Processing Service
    /// </summary>
    /// <param name="ct"></param>
    /// <returns></returns>
    protected override async Task ExecuteAsync(CancellationToken ct)
    {
        if (_logger.IsEnabled(LogLevel.Information) is true)
        {
            _logger.LogInformation("Task Processing Service is running.");
        }

        var tasks = new Task[_workerCount];
        for (int i = 0; i < _workerCount; i++)
        {
            int workerId = i; // Capture the loop variable
            tasks[i] = Task.Run(() => BackgroundProcessing(ct, workerId));
        }

        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// Main logic routine for the background processing service. 
    /// Every second a worker checks it's own folder for task files to process.
    /// As files are queued to the folder, the worker processes them.
    /// Successful tasks are deleted and removed from queue.
    /// Failures are requeued to be processed at a later time. 
    /// </summary>
    /// <param name="ct"></param>
    /// <param name="workerId"></param>
    /// <returns></returns>
    private async Task BackgroundProcessing(CancellationToken ct, int workerId)
    {
        if (_logger.IsEnabled(LogLevel.Information) is true)
        {
            _logger.LogInformation($"Worker {workerId} started.");
        }

        while (!ct.IsCancellationRequested)
        {
            var taskItem = await _taskQueue.FetchTasksAsync(workerId, ct);

            if (taskItem != null)
            {
                try
                {
                    CurrentContext.User = taskItem.User;

                    switch (taskItem.TaskType)
                    {
                        case "ComputeEmbeddings":
                            await ComputeEmbeddingsAsync(taskItem.Task,
                                                         taskItem.Original,
                                                         taskItem.RetryCount,
                                                         taskItem.User.PDL!);
                            break;
                        case "RemoveFromIndex":
                            await RemoveFromIndexAsync(taskItem.Task, 
                                                       taskItem.User.PDL!);
                            break;
                        default:
                            _logger.LogWarning($"Unknown task type: {taskItem.TaskType}");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    LogError(ex, $"Worker {workerId} encountered an error executing work item.");
                    {
                        if (taskItem.RetryCount < _retryLimit)
                        {
                            taskItem.RetryCount++;
                            await _taskQueue.QueueItemAsync(
                                taskItem.Clone(), DateTime.UtcNow.AddMinutes(1));
                        }
                        else
                        {
                            var blobClient = GetContainerClient(taskItem.User.PDL!)
                                .GetBlobClient(Path.ChangeExtension(taskItem.Task, taskItem.Original));

                            await blobClient.SetMetadataAsync(
                                new Dictionary<string, string> { 
                                    { "processed", Processed.Error.ToString().ToLower() } });
                        }
                    }
                }
                finally
                {
                    _taskQueue.DeleteTaskFile(taskItem.Path);
                }
            }
            await Task.Delay(1000, ct);
        }

        if (_logger.IsEnabled(LogLevel.Information) is true)
        {
            _logger.LogInformation($"Worker {workerId} stopping.");
        }
    }

    /// <summary>
    /// Stops the Task Processing Service
    /// </summary>
    /// <param name="ct">CancellationToken</param>
    /// <returns></returns>
    public override async Task StopAsync(CancellationToken ct)
    {
        if (_logger.IsEnabled(LogLevel.Information) is true)
        {
            _logger.LogInformation("Task Processing Service is stopping.");
        }
        await base.StopAsync(ct);
    }

    /// <summary>
    /// Task logic for computing the embeddings for a document and adding it to the search index
    /// </summary>
    /// <param name="blob"></param>
    /// <returns></returns>
    private async ValueTask ComputeEmbeddingsAsync(string blob,
                                                   string original,
                                                   int attempt = 0,
                                                   string PDL = "Default")
    {
        var blobClient = GetContainerClient(PDL).GetBlobClient(blob);

        try
        {
            var parts = blob.Split('/');
            var oid = parts[0];
            var workspaceId = parts[1];

            using (MemoryStream stream = new MemoryStream())
            {
                await using (var blobStream = await blobClient.OpenReadAsync())
                {
                    await blobStream.CopyToAsync(stream);
                }

                IReadOnlyList<PageDetail> pageMap = null!;

                try
                {
                    pageMap = await _docService.GetDocumentTextAsync(stream);
                }
                catch (Exception ex)
                {
                    LogError(ex, "Failed to get document text", blobClient.Name);

                    await UpdateBlobMetadataAsync(blobClient, original, Processed.Error.ToString().ToLower(), PDL);
                    return;
                }

                var sections = _docService.CreateSections(pageMap, oid, workspaceId, blobClient.Name, attempt);
                await _searchService.AddToSearchAsync(sections, PDL);

                await UpdateBlobMetadataAsync(blobClient, original, Processed.True.ToString().ToLower(), PDL);
            }
        }
        catch (RequestFailedException ex) when (ex.ErrorCode == "BlobNotFound")
        {
            LogError(ex, "Failed to embed blob", blobClient.Name);
        }
        catch (Exception ex)
        {
            LogError(ex, "Failed to embed blob", blobClient.Name);
            throw;
        }
    }

    /// <summary>
    /// Task logic for removing a document from the search index
    /// </summary>
    /// <param name="blob"></param>
    /// <returns></returns>
    private async ValueTask RemoveFromIndexAsync(string blob, string PDL = "Default")
    {
        try
        {
            if (Path.GetExtension(blob) != ".pdf")
            {
                blob = Path.ChangeExtension(blob, ".pdf");
                var blobClient = GetContainerClient(PDL).GetBlobClient(blob);
                await blobClient.DeleteAsync();
            }

            while (true)
            {
                var filter = $"sourcefile eq '{blob?.Replace("'", "''")}'";

                var response = await _searchService.GetSearchClient(PDL).SearchAsync<SearchDocument>("",
                    new SearchOptions
                    {
                        Filter = filter,
                        Size = 1_000
                    });

                var documentsToDelete = new List<SearchDocument>();
                await foreach (var result in response.Value.GetResultsAsync())
                {
                    documentsToDelete.Add(new SearchDocument
                    {
                        ["id"] = result.Document["id"]
                    });
                }

                if (documentsToDelete.Count == 0) {  break; }

                Response<IndexDocumentsResult> deleteResponse =
                    await _searchService.GetSearchClient(PDL).DeleteDocumentsAsync(documentsToDelete);

                await Task.Delay(TimeSpan.FromMilliseconds(2_000));
            }
        }
        catch (RequestFailedException ex) when (ex.ErrorCode == "BlobNotFound")
        {
            LogError(ex, "Failed to remove blob from index", blob);
        }
        catch (Exception ex)
        {
            LogError(ex, "Failed to remove blob from index", blob);
            throw;
        }
    }

    /// <summary>
    /// Updates the metadata of a blob with the given status
    /// </summary>
    /// <param name="blobClient"></param>
    /// <param name="original"></param>
    /// <param name="status"></param>
    /// <returns></returns>
    private async Task UpdateBlobMetadataAsync(BlobClient blobClient, string original, string status, string PDL = "Default")
    {
        if (original != ".pdf")
        {
            blobClient = GetContainerClient(PDL).GetBlobClient(Path.ChangeExtension(blobClient.Name, original));
        }

        await blobClient.SetMetadataAsync(new Dictionary<string, string> { { "processed", status } });
    }

    /// <summary>
    /// Logs an error message to the logger
    /// </summary>
    /// <param name="ex"></param>
    /// <param name="message"></param>
    /// <param name="blobName"></param>
    private void LogError(Exception ex, string message, string blobName = null!)
    {
        var logMessage = $@"
            Exception Type: {ex.GetType().FullName}
            Message: {message}
            Stack Trace: {ex.StackTrace}";

        if (!string.IsNullOrEmpty(blobName))
        {
            logMessage += $"\nBlob Name: {blobName}";
        }

        _logger.LogError(ex, logMessage);
    }
}