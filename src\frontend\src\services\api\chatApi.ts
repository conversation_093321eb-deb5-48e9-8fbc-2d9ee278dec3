import { api } from '../../app/api';
import { ChatMessageProps } from '../../utils/types';

// Define the chat request type
export interface ChatRequest {
  workspace_id: string | null;
  settings: object;
  messages: {
    role: string;
    content: {
      type: string;
      value: string | object;
    }[];
  }[];
}

// Define chat history types for RTK Query
export interface ChatHistoryItem {
  id: string;
  text: string;
  date: string;
  workspaceId: string;
  messages?: ChatMessageProps[];
}

export interface UpdateChatHistoryRequest {
  chatId: string;
  messages: ChatMessageProps[];
}

export const chatApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Send a chat message and get a streaming response
    // Note: RTK Query doesn't natively support streaming responses,
    // so we'll need to handle this differently
    sendChatMessage: builder.mutation<string, ChatRequest>({
      queryFn: async (chatRequest, { getState }) => {
        try {
          // Get the base URL and token from the state
          const state = getState() as any;
          const token = state.token.token;
          const baseUrl = import.meta.env.VITE_API_URL;

          // Make the fetch request manually to handle streaming
          const response = await fetch(`${baseUrl}/Chat`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(chatRequest),
          });

          if (!response.ok) {
            throw new Error(`Error ${response.status}: ${response.statusText}`);
          }

          // Return a success response
          // The actual streaming will be handled by the component
          return { data: 'Streaming response initiated' };
        } catch (error) {
          return {
            error: {
              status: 'CUSTOM_ERROR',
              error: error instanceof Error ? error.message : 'Unknown error',
            },
          };
        }
      },
      invalidatesTags: ['Chat' as const],
    }),

    // Chat history management following RTK Query patterns
    // This is a local API that manages IndexedDB chat history
    updateChatHistory: builder.mutation<void, UpdateChatHistoryRequest>({
      queryFn: async ({ chatId, messages }) => {
        try {
          // Import the recentChatTabsData function dynamically to avoid circular imports
          const { recentChatTabsData } = await import('../../utils/recentChatTabsData');
          const { getAllData } = await import('../../db/chatDB');
          const { Stores } = await import('../../constants/dbConstants');

          // Get current database state
          const allDbData = await getAllData<{ hash: string, id: string, date: string, workspaceId: string }>(Stores.Users);

          // Create allChatHistory object with the updated chat
          const allChatHistory = {
            allChat: {
              [chatId]: messages
            }
          };

          // This will be handled by the component that calls this mutation
          // The component should dispatch the result to Redux
          return { data: undefined };
        } catch (error) {
          return {
            error: {
              status: 'CUSTOM_ERROR',
              error: error instanceof Error ? error.message : 'Failed to update chat history',
            },
          };
        }
      },
      invalidatesTags: ['Chat' as const],
    }),

    // Refresh chat history - triggers a complete reload
    refreshChatHistory: builder.mutation<void, void>({
      queryFn: async () => {
        try {
          // This mutation will trigger a refresh of the chat history
          // The actual work will be done by the component using this mutation
          return { data: undefined };
        } catch (error) {
          return {
            error: {
              status: 'CUSTOM_ERROR',
              error: error instanceof Error ? error.message : 'Failed to refresh chat history',
            },
          };
        }
      },
      invalidatesTags: ['Chat' as const],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useSendChatMessageMutation,
  useUpdateChatHistoryMutation,
  useRefreshChatHistoryMutation,
} = chatApi;

// Export a utility function to handle streaming chat responses
export const streamChatResponse = async (
  chatRequest: ChatRequest,
  token: string,
  onDataChunk: (chunk: string) => void
): Promise<void> => {
  try {
    const baseUrl = import.meta.env.VITE_API_URL;
    const response = await fetch(`${baseUrl}/Chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(chatRequest),
    });

    if (!response.ok) {
      throw new Error(`Error ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Response body is null');
    }

    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      onDataChunk(chunk);
    }
  } catch (error) {
    console.error('Error streaming chat response:', error);
    throw error;
  }
};
