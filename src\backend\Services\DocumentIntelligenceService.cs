﻿using Azure;
using Backend.Models;
using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using Azure.AI.FormRecognizer.DocumentAnalysis;

namespace Backend.Services
{
    public sealed partial class DocumentIntelligenceService : IDocumentIntelligenceService
    {
        [GeneratedRegex("[^0-9a-zA-Z_-]")]
        private static partial Regex MatchInSetRegex();
        private static readonly char[] _sentenceEndings = new[] { '.', '!', '?' };
        private static readonly char[] _wordBreaks = new[] { ',', ';', ':', ' ', '(', ')', '[', ']', '{', '}', '\t', '\n', '<', '>' };

        private readonly DocumentAnalysisClient _docClient;
        private readonly ILogger<DocumentIntelligenceService> _logger;
        private readonly int _sectionOverlap;

        public DocumentIntelligenceService(DocumentAnalysisClient docClient,
                                           ILogger<DocumentIntelligenceService> logger,
                                           int sectionOverlap)
        {
            _docClient = docClient;
            _logger = logger;
            _sectionOverlap = sectionOverlap;
        }

        /// <summary>
        /// Get the text from the given document stream.
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        public async Task<IReadOnlyList<PageDetail>> GetDocumentTextAsync(MemoryStream stream)
        {
            var offset = 0;
            List<PageDetail> pageMap = [];
            stream.Seek(0, SeekOrigin.Begin);

            // Create a CancellationTokenSource with a 60-second timeout
            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(60));
            AnalyzeDocumentOperation operation;

            try
            {
                operation = await _docClient.AnalyzeDocumentAsync(
                    WaitUntil.Completed,
                    "prebuilt-layout",
                    stream,
                    cancellationToken: cts.Token);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                throw;
            }

            AnalyzeResult result = operation.Value;

            for (var i = 0; i < result.Pages.Count; i++)
            {
                var tablesOnPage = result.Tables.Where(t => t.BoundingRegions[0].PageNumber == i + 1).ToList();
                var tableChars = GetTableChars(result.Pages[i], tablesOnPage);
                var pageText = ReplaceTablesWithHtml(result, result.Pages[i].Spans[0].Index, tableChars, tablesOnPage);

                pageMap.Add(new PageDetail(i, offset, pageText.ToString()));
                offset += pageText.Length;
            }
            return pageMap.AsReadOnly();
        }

        /// <summary>
        /// Get the table characters for the given page.
        /// </summary>
        /// <param name="page"></param>
        /// <param name="tablesOnPage"></param>
        /// <returns></returns>
        private static int[] GetTableChars(DocumentPage page, List<DocumentTable> tablesOnPage)
        {
            int pageIndex = page.Spans[0].Index;
            int pageLength = page.Spans[0].Length;
            int[] tableChars = Enumerable.Repeat(-1, pageLength).ToArray();

            for (var tableId = 0; tableId < tablesOnPage.Count; tableId++)
            {
                foreach (DocumentSpan span in tablesOnPage[tableId].Spans)
                {
                    for (var j = 0; j < span.Length; j++)
                    {
                        int index = span.Index - pageIndex + j;
                        if (index >= 0 && index < pageLength)
                        {
                            tableChars[index] = tableId;
                        }
                    }
                }
            }
            return tableChars;
        }

        /// <summary>
        /// Replaces tables in the page text with HTML tables.
        /// </summary>
        /// <param name="result"></param>
        /// <param name="pageIndex"></param>
        /// <param name="tableChars"></param>
        /// <param name="tablesOnPage"></param>
        /// <returns></returns>
        private string ReplaceTablesWithHtml(AnalyzeResult result,
                                             int pageIndex,
                                             int[] tableChars,
                                             List<DocumentTable> tablesOnPage)
        {
            StringBuilder pageText = new();
            HashSet<int> addedTables = new();

            for (int j = 0; j < tableChars.Length; j++)
            {
                if (tableChars[j] == -1)
                {
                    pageText.Append(result.Content[pageIndex + j]);
                }
                else if (!addedTables.Contains(tableChars[j]))
                {
                    pageText.Append(TableToHtml(tablesOnPage[tableChars[j]]));
                    addedTables.Add(tableChars[j]);
                }
            }
            pageText.Append(' ');
            return pageText.ToString();
        }

        /// <summary>
        /// Creates a list of sections from a list of page details.
        /// </summary>
        /// <param name="pageMap"></param>
        /// <param name="oid"></param>
        /// <param name="workspaceId"></param>
        /// <param name="blob"></param>
        /// <returns></returns>
        public IEnumerable<Section> CreateSections(IReadOnlyList<PageDetail> pageMap,
                                                   string oid,
                                                   string workspaceId,
                                                   string blob,
                                                   int attempt = 0)
        {
            var allText = string.Concat(pageMap.Select(p => p.Text));
            var length = allText.Length;

            if (_logger.IsEnabled(LogLevel.Information))
            {
                _logger.LogInformation("Splitting '{Blob}' into sections by page", blob);
            }

            for (int i = 0; i < pageMap.Count; i++)
            {
                var start = Math.Max(0, pageMap[i].Offset - _sectionOverlap);
                var end = Math.Min(length, pageMap[i].Offset + pageMap[i].Text.Length + _sectionOverlap);

                var pageText = allText[start..end];
                var max = Math.Max(10_000, 20_000 - (2_500 * attempt));
                foreach (var section in SplitIntoSections(pageText, blob, oid, workspaceId, start, (i + 1), max))
                {
                    yield return section;
                }
            }
        }

        /// <summary>
        /// Splits the given text into sections.
        /// </summary>
        /// <param name="text"></param>
        /// <param name="blob"></param>
        /// <param name="oid"></param>
        /// <param name="workspaceId"></param>
        /// <param name="start"></param>
        /// <param name="i"></param>
        /// <returns></returns>
        private IEnumerable<Section> SplitIntoSections(string text,
                                                       string blob,
                                                       string oid,
                                                       string workspaceId,
                                                       int start,
                                                       int i,
                                                       int max = 20_000)
        {
            if (text.Length <= max)
            {
                yield return new Section(
                    Id: MatchInSetRegex().Replace($"{blob}-{start}", "_").TrimStart('_'),
                    Content: text,
                    Workspace: workspaceId,
                    SourcePage: blob.Replace(oid + "/" + workspaceId, "").Replace("/", "") + "#page=" + i,
                    SourceFile: blob);
            }
            else
            {
                var mid = text.Length / 2;
                var splitPoint = FindSplitPoint(text, mid, max);

                var firstPart = text[..Math.Min(splitPoint + _sectionOverlap, text.Length)];
                var secondPart = text[Math.Max(splitPoint - _sectionOverlap, 0)..];

                foreach (var section in SplitIntoSections(firstPart, blob, oid, workspaceId, start, i))
                {
                    yield return section;
                }

                foreach (var section in SplitIntoSections(secondPart, blob, oid, workspaceId, splitPoint, i))
                {
                    yield return section;
                }
            }
        }

        /// <summary>
        /// Aims to find a suitable point in the provided text to split into 2 sections. 
        /// Tries to split at a sentence ending near the middle of the text.
        /// </summary>
        /// <param name="text"></param>
        /// <param name="mid"></param>
        /// <returns></returns>
        private int FindSplitPoint(string text, int mid, int max = 20_000)
        {
            var lastWord = -1;
            var split = mid;

            while (split < text.Length && !_sentenceEndings.Contains(text[split]))
            {
                if (_wordBreaks.Contains(text[split])) 
                { 
                    lastWord = split; 
                }
                split++;
            }

            if (split < text.Length && lastWord > 0 &&
                !_sentenceEndings.Contains(text[split]))
            {
                split = lastWord;
            }

            if (split <= 0 || 
                split >= text.Length || 
                split >= max - _sectionOverlap) { return mid; }
            else { return split;}
        }

        /// <summary>
        /// Takes a table object and converts it to an HTML table.
        /// </summary>
        /// <param name="table"></param>
        /// <returns></returns>
        private static string TableToHtml(DocumentTable table)
        {
            var tableHtml = new StringBuilder("<table>");
            var rows = new List<DocumentTableCell>[table.RowCount];
            for (int i = 0; i < table.RowCount; i++)
            {
                rows[i] = table.Cells.Where(c => c.RowIndex == i)
                                     .OrderBy(c => c.ColumnIndex)
                                     .ToList();
            }

            foreach (var rowCells in rows)
            {
                tableHtml.Append("<tr>");
                foreach (DocumentTableCell cell in rowCells)
                {
                    var tag = (cell.Kind == "columnHeader" || cell.Kind == "rowHeader") ? "th" : "td";
                    var cellSpans = string.Empty;

                    if (cell.ColumnSpan > 1)
                    {
                        cellSpans += $" colSpan='{cell.ColumnSpan}'";
                    }

                    if (cell.RowSpan > 1)
                    {
                        cellSpans += $" rowSpan='{cell.RowSpan}'";
                    }

                    tableHtml.AppendFormat(
                        "<{0}{1}>{2}</{0}>", tag, cellSpans, WebUtility.HtmlEncode(cell.Content));
                }
                tableHtml.Append("</tr>");
            }
            tableHtml.Append("</table>");
            return tableHtml.ToString();
        }
    }
}