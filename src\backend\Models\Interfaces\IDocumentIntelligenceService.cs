﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for Document Intelligence Service
    /// </summary>
    public interface IDocumentIntelligenceService
    {
        /// <summary>
        /// Get the text from the given document stream.
        /// </summary>
        /// <param name="stream"></param>
        /// <returns></returns>
        Task<IReadOnlyList<PageDetail>> GetDocumentTextAsync(MemoryStream stream);

        /// <summary>
        /// Creates a list of sections from a list of page details.
        /// </summary>
        /// <param name="pageMap"></param>
        /// <param name="oid"></param>
        /// <param name="workspaceId"></param>
        /// <param name="blob"></param>
        /// <returns></returns>
        IEnumerable<Section> CreateSections(IReadOnlyList<PageDetail> pageMap,
                                            string oid,
                                            string workspaceId,
                                            string blob,
                                            int attempt = 0);
    }
}
