﻿#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0001

using Azure;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Models;
using Backend.Models;
using Backend.Models.Context;
using Backend.Services;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Moq;

namespace BackendUnitTests
{
    public class SearchTests
    {
        private readonly Mock<SearchIndexClient> _mockSearchIndexClient;
        private readonly Dictionary<string, SearchClient> _mockSearchClients;
        private readonly Mock<ITextEmbeddingGenerationService> _mockTextEmbeddingService;
        private readonly Mock<ILogger<SearchService>> _mockLogger;
        private readonly SearchService _searchService;

        public SearchTests()
        {
            _mockSearchIndexClient = new Mock<SearchIndexClient>();
            _mockSearchClients = new Dictionary<string, SearchClient>
            {
                { "Default", new Mock<SearchClient>().Object }
            };
            _mockTextEmbeddingService = new Mock<ITextEmbeddingGenerationService>();
            _mockLogger = new Mock<ILogger<SearchService>>();

            CurrentContext.User = new User
            {
                OID = "test-oid",
                DisplayName = "test-oid",
                Images = false,
                ChatCount = 0,
                PDL = "NAM"
            };

            _searchService = new SearchService(
                _mockSearchClients,
                _mockTextEmbeddingService.Object,
                _mockLogger.Object,
                7000,
                3);
        }

        [Fact]
        public async Task QueryDocumentsAsync_ReturnsContentItems_WhenQueryIsProvided()
        {
            // Arrange
            var workspace = "test-workspace";
            var searchSettings = new SearchSettings
            {
                Top = 10,
                MinimumSearchScore = 0.5f,
                MinimumRerankerScore = 0.5f,
                UseSemanticRanker = false,
                UseSemanticCaptions = false,
                RetrievalMode = RetrievalMode.Text
            };

            var searchDocuments = new List<SearchDocument>
            {
                new SearchDocument
                {
                    ["sourcepage"] = "page1",
                    ["content"] = "This is a test content."
                }
            };

            var searchResultItems = searchDocuments.Select(doc => SearchModelFactory.SearchResult(doc, 0.6, null)).ToList();

            var mockSearchResults = SearchModelFactory.SearchResults(searchResultItems, null, null, null, null);

            var mockResponse = new Mock<Response<SearchResults<SearchDocument>>>();
            mockResponse.Setup(r => r.Value).Returns(mockSearchResults);

            var mockSearchClient = Mock.Get(_mockSearchClients["Default"]);
            mockSearchClient
                .Setup(client => client.SearchAsync<SearchDocument>(It.IsAny<string>(),
                                                                    It.IsAny<SearchOptions>(),
                                                                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockResponse.Object);

            // Act
            var result = await _searchService.QueryDocumentsAsync(workspace, searchSettings, "test query");

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("page1", result[0].Title);
            Assert.Equal("This is a test content.", result[0].Content);
        }

        [Fact]
        public async Task AddToSearchAsync_IndexesSuccessfully()
        {
            // Arrange
            var sections = new List<Section>
            {
                new Section
                {
                    Id = "1_1",
                    Content = "Test content 1",
                    Workspace = "Workspace1",
                    SourcePage = "Page1",
                    SourceFile = "File1"
                },
                new Section
                {
                    Id = "2_2",
                    Content = "Test content 2",
                    Workspace = "Workspace2",
                    SourcePage = "Page2",
                    SourceFile = "File2"
                }
            };

            _mockTextEmbeddingService
                .Setup(s => s.GenerateEmbeddingsAsync(
                    It.IsAny<IList<string>>(),
                    It.IsAny<Kernel>(),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<ReadOnlyMemory<float>> { new float[] { 0.1f, 0.2f, 0.3f } });

            var mockIndexDocumentsResult = SearchModelFactory.IndexDocumentsResult(
                new List<IndexingResult>
                {
                    SearchModelFactory.IndexingResult("1_1", It.IsAny<string>(), true, 200),
                    SearchModelFactory.IndexingResult("2_2", It.IsAny<string>(), true, 200)
                });

            var mockIndexDocumentsResponse = new Mock<Response<IndexDocumentsResult>>();
            mockIndexDocumentsResponse.Setup(r => r.Value).Returns(mockIndexDocumentsResult);

            var mockSearchClient = Mock.Get(_mockSearchClients["Default"]);
            mockSearchClient
                .Setup(client =>
                    client.IndexDocumentsAsync(It.IsAny<IndexDocumentsBatch<SearchDocument>>(),
                                               It.IsAny<IndexDocumentsOptions>(),
                                               It.IsAny<CancellationToken>()))
                .ReturnsAsync(mockIndexDocumentsResponse.Object);

            // Act
            await _searchService.AddToSearchAsync(sections);

            // Assert
            mockSearchClient.Verify(client =>
                client.IndexDocumentsAsync(It.IsAny<IndexDocumentsBatch<SearchDocument>>(),
                                           It.IsAny<IndexDocumentsOptions>(),
                                           It.IsAny<CancellationToken>()),
                                           Times.AtLeastOnce);
        }
    }
}