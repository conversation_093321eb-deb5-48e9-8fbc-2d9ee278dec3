﻿#pragma warning disable SKEXP0010

using Backend.Services;
using Microsoft.SemanticKernel;

namespace Backend.Startup
{
    internal static partial class Startup
    {
        internal static void ConfigureKernel(this IServiceCollection services, IConfiguration config)
        {
            services.AddSingleton<Kernel>(sp =>
            {
                var kernelBuilder = Kernel.CreateBuilder();

                // Create an HttpClient with a custom message handler to log OpenAI response data
                var httpClient = new HttpClient(
                    new OpenAIMessageHandler(
                        sp.GetRequiredService<ILogger<OpenAIService>>(),
                        config["AzureOpenAI:Endpoint"]!.TrimEnd('/') + "/openai"));

                // Add Chat Completion Service
                kernelBuilder.AddAzureOpenAIChatCompletion(
                    config["AzureOpenAI:Deployment"]!,
                    config["AzureOpenAI:Endpoint"]!,
                    config["GallagherAI-OpenAIKey"]!,
                    httpClient: httpClient
                );

                // Add Text Embedding Service
                kernelBuilder.AddAzureOpenAITextEmbeddingGeneration(
                    deploymentName: config["AzureOpenAI:Embeddings"]!,
                    apiKey: config["GallagherAI-OpenAIKey"]!,
                    endpoint: config["AzureOpenAI:Endpoint"]!,
                    dimensions: config.GetValue<int>("AzureOpenAI:Dimensions")!,
                    httpClient: httpClient
                );

                // Build the kernel
                var kernel = kernelBuilder.Build();

                // Add Plugins
                //kernel.Plugins.AddFromObject(new TimePlugin());

                // Example Call
                // kernel.Plugins.GetFunction("Plugin", "Function").InvokeAsync(kernel);

                return kernel;
            });
        }
    }
}
