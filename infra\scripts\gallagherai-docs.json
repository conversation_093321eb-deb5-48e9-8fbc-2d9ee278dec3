{"name": "gallagherai-docs", "fields": [{"name": "id", "type": "Edm.String", "searchable": false, "filterable": false, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": true}, {"name": "content", "type": "Edm.String", "searchable": true, "filterable": false, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false, "analyzer": "en.microsoft"}, {"name": "embedding", "type": "Collection(Edm.Single)", "searchable": true, "filterable": false, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false, "dimensions": 512, "vectorSearchProfile": "embedding_config"}, {"name": "workspace", "type": "Edm.String", "searchable": false, "filterable": true, "retrievable": true, "stored": true, "sortable": false, "facetable": true, "key": false}, {"name": "sourcepage", "type": "Edm.String", "searchable": false, "filterable": true, "retrievable": true, "stored": true, "sortable": false, "facetable": true, "key": false}, {"name": "sourcefile", "type": "Edm.String", "searchable": false, "filterable": true, "retrievable": true, "stored": true, "sortable": false, "facetable": true, "key": false}, {"name": "storageUrl", "type": "Edm.String", "searchable": false, "filterable": true, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false}, {"name": "oids", "type": "Collection(Edm.String)", "searchable": false, "filterable": true, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false}, {"name": "groups", "type": "Collection(Edm.String)", "searchable": false, "filterable": true, "retrievable": true, "stored": true, "sortable": false, "facetable": false, "key": false}], "semantic": {"configurations": [{"name": "default", "prioritizedFields": {"titleField": null, "prioritizedContentFields": [{"fieldName": "content"}], "prioritizedKeywordsFields": []}}]}, "vectorSearch": {"algorithms": [{"name": "hnsw-config", "kind": "hnsw", "hnswParameters": {"metric": "cosine", "m": 4, "efConstruction": 400, "efSearch": 500}}], "profiles": [{"name": "embedding_config", "algorithm": "hnsw-config"}]}}