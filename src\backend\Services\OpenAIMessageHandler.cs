﻿using Backend.Models.Context;
using Backend.Models.TokenLogging;
using System.Text.Json;

namespace Backend.Services
{
    /// <summary>
    /// Custom message handler to log OpenAI response data, such as token usage.
    /// </summary>
    public class OpenAIMessageHandler : HttpClientHandler
    {
        private readonly ILogger _logger;
        private readonly string _endpoint;

        /// <summary>
        /// Initializes a new instance of the <see cref="OpenAIMessageHandler"/> class.
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="endpoint">Azure Open AI endpoint</param>
        public OpenAIMessageHandler(ILogger logger, string endpoint)
        {
            _logger = logger;
            _endpoint = endpoint;
        }

        /// <summary>
        /// Send the request and log the response content if the request is to the OpenAI endpoint.
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request,
                                                                     CancellationToken cancellationToken)
        {
            // Log the response content only if the request is to the OpenAI endpoint.
            if (request.RequestUri != null && request.RequestUri.ToString().StartsWith(_endpoint))
            {
                var response = await base.SendAsync(request, cancellationToken);

                if (response.Content != null)
                {
                    var memoryStream = new MemoryStream();
                    await response.Content.CopyToAsync(memoryStream);
                    memoryStream.Position = 0;

                    using (var reader = new StreamReader(memoryStream, leaveOpen: true))
                    {
                        string content = await reader.ReadToEndAsync();

                        // Log the usage details if provided.
                        if (!content.ToLower().StartsWith("data") && content.Contains("usage"))
                        {
                            LogContent(content);
                        }
                    }

                    // Reset response content so that it can be re-used.
                    memoryStream.Position = 0;
                    response.Content = new StreamContent(memoryStream);
                }
                return response;
            }
            else
            {
                return await base.SendAsync(request, cancellationToken);
            }
        }

        /// <summary>
        /// Log the OpenAI response content.
        /// </summary>
        /// <param name="content"></param>
        private void LogContent(string content)
        {
            try
            {
                var result = JsonSerializer.Deserialize<Response>(content);

                // Determine the type of the response
                var type = result?.Data?.FirstOrDefault()?.Object ?? result?.Object;

                if (type == "chat.completion")
                {
                    // Log the chat completion information
                    _logger.LogInformation("OpenAI request: User: {User}, Type: {Type}, Model: {Model}, " +
                        "Images: {Images}, ChatCount: {ChatCount}, InputTokens: {InputTokenCount}, OutputTokens: {OutputTokenCount}, TotalTokens: {TotalTokenCount}",
                        CurrentContext.User.DisplayName,
                        type,
                        result?.Model,
                        CurrentContext.User.Images,
                        CurrentContext.User.ChatCount,
                        result?.Usage?.PromptTokens ?? 0,
                        result?.Usage?.CompletionTokens ?? 0,
                        result?.Usage?.TotalTokens ?? 0);
                }
                else
                {
                    // Log the text embedding information 
                    _logger.LogInformation("OpenAI request: User: {User}, Type: {Type}, Model: {Model}, " +
                        "InputTokens: {InputTokenCount}, OutputTokens: {OutputTokenCount}, TotalTokens: {TotalTokenCount}",
                        CurrentContext.User.DisplayName,
                        type,
                        result?.Model,
                        result?.Usage?.PromptTokens ?? 0,
                        result?.Usage?.CompletionTokens ?? 0,
                        result?.Usage?.TotalTokens ?? 0);
                }
            }
            catch (JsonException ex)
            {
                _logger.LogError("Failed to parse OpenAI request: {Message}", ex.Message);
            }
        }
    }
}