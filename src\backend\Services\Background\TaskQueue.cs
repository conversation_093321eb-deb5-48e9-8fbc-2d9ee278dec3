﻿using Backend.Models;
using System.Text.Json;

public class TaskQueue : ITaskQueue
{
    private readonly string _baseQueueFolder;
    private readonly int _workerCount;
    private int _currentWorker;

    /// <summary>
    /// Initializes a new instance of the <see cref="TaskQueue"/> class.
    /// </summary>
    /// <param name="workerCount">Number of concurrent workers to proces task items</param>
    /// <param name="basePath">Base file path to the file based queue</param>
    public TaskQueue(int workerCount, string basePath)
    {
        _baseQueueFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.UserProfile), basePath);
        _workerCount = workerCount;
        _currentWorker = 0;

        for (int i = 0; i < _workerCount; i++)
        {
            Directory.CreateDirectory(Path.Combine(_baseQueueFolder, $"worker_{i}"));
        }
    }

    /// <summary>
    /// Queues a task to be processed by a worker, assigned round robin style.
    /// </summary>
    /// <param name="taskItem">TaskItem object containing the details of the task</param>
    /// <param name="timestamp">Optional parameter to set the time in which to process the task</param>
    /// <returns></returns>
    public async ValueTask QueueItemAsync(TaskItem taskItem, DateTime? timestamp = null)
    {
        string file = $"{DateTime.UtcNow:yyyyMMddHHmmssfff}_{Guid.NewGuid()}_{_currentWorker}.json";
        string temp = Path.Combine(_baseQueueFolder, file);
        string dest = Path.Combine(Path.Combine(_baseQueueFolder, $"worker_{_currentWorker}"), file);

        var json = JsonSerializer.Serialize(taskItem);

        // Write to a temporary file first
        await File.WriteAllTextAsync(temp, json);

        // Move the temporary file to the final location
        File.Move(temp, dest);

        _currentWorker = (_currentWorker + 1) % _workerCount;
    }

    /// <summary>
    /// Deserializes a task file and returns the task details.
    /// </summary>
    /// <param name="workerId">WorkerId</param>
    /// <param name="ct">CancellationToken</param>
    /// <returns></returns>
    public async Task<TaskItem> FetchTasksAsync(int workerId, CancellationToken ct)
    {
        var workerFolder = Path.Combine(_baseQueueFolder, $"worker_{workerId}");
        var files = Directory.GetFiles(workerFolder)
                             .Where(f => DateTime.TryParseExact(Path.GetFileName(f).Split('_')[0],
                                                                "yyyyMMddHHmmssfff",
                                                                null,
                                                                System.Globalization.DateTimeStyles.None,
                                                                out var timestamp) && timestamp <= DateTime.UtcNow)
                             .OrderBy(f => f)
                             .ToList();

        foreach (var file in files)
        {
            try
            {
                var json = await File.ReadAllTextAsync(file);
                TaskItem taskItem = JsonSerializer.Deserialize<TaskItem>(json)!;
                taskItem.Path = file;
                return taskItem!;
            }
            catch (Exception)
            {
                continue;
            }
        }

        return null!;
    }

    /// <summary>
    /// Deletes a task file.
    /// </summary>
    /// <param name="filePath">Path to the task file</param>
    public void DeleteTaskFile(string filePath)
    {
        try
        {
            File.Delete(filePath);
        }
        catch (IOException ex)
        {
            // Log the exception or handle it as needed
            Console.WriteLine(ex.Message);
        }
    }
}