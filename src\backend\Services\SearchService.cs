﻿#pragma warning disable SKEXP0010
#pragma warning disable SKEXP0001

using Azure.Search.Documents;
using Azure.Search.Documents.Models;
using Backend.Models;
using Microsoft.SemanticKernel.Embeddings;
using Backend.Models.Context;

namespace Backend.Services
{
    public class SearchService : ISearchService
    {
        private readonly Dictionary<string, SearchClient> _searchClients;
        private readonly ITextEmbeddingGenerationService _textEmbeddingService;
        private readonly ILogger<SearchService> _logger;
        private readonly int _minimumContextLength;
        private readonly int _top;

        public SearchService(Dictionary<string, SearchClient> searchClients,
                             ITextEmbeddingGenerationService textEmbeddingService,
                             ILogger<SearchService> logger,
                             int minimumContextLength,
                             int top)
        {
            _searchClients = searchClients;
            _textEmbeddingService = textEmbeddingService;
            _logger = logger;
            _minimumContextLength = minimumContextLength;
            _top = top;
        }

        public SearchClient GetSearchClient(string val)
        {
            return _searchClients.TryGetValue(
                val, out var client) ? client : _searchClients["Default"];
        }

        /// <summary>
        /// Queries the search index for information.
        /// </summary>
        /// <param name="workspace">Workspace</param>
        /// <param name="settings">Search Settings</param>
        /// <param name="textQuery">Textual query to be used for text based search</param>
        /// <param name="vector">Vector query to be used for the vector based search</param>
        /// <param name="ct">CancellationToken</param>
        /// <returns></returns>
        /// <exception cref="InvalidOperationException"></exception>
        public async Task<SectionItem[]> QueryDocumentsAsync(
            string workspace,
            SearchSettings settings,
            string? textQuery = null,
            float[]? vector = null,
            CancellationToken ct = default)
        {
            if (textQuery is null && vector is null)
            {
                throw new ArgumentException("Either a textual query or vector must be provided");
            }

            // Create search filter and options.
            var filter = CreateFilter(CurrentContext.User.OID!, workspace);
            var options = CreateSearchOptions(settings, filter, vector);

            // Search
            ct.ThrowIfCancellationRequested();
            var searchResultResponse = 
                await GetSearchClient(CurrentContext.User.PDL!)
                    .SearchAsync<SearchDocument>(textQuery, options, ct);

            if (searchResultResponse.Value is null)
            {
                throw new InvalidOperationException("Failed to get search result.");
            }

            SearchResults<SearchDocument> searchResult = searchResultResponse.Value;

            // Assemble results.
            var sb = new List<SectionItem>();
            int totalContentLength = 0;

            foreach (var doc in searchResult.GetResults())
            {
                if ((settings.UseSemanticRanker == false &&
                    doc.Score >= settings.MinimumSearchScore) ||
                    (settings.UseSemanticRanker == true &&
                    doc.SemanticSearch.RerankerScore >= settings.MinimumRerankerScore))
                {
                    doc.Document.TryGetValue("sourcepage", out var sourcePageValue);
                    string? contentValue;
                    try
                    {
                        if (settings.UseSemanticCaptions)
                        {
                            var docs = doc.SemanticSearch.Captions.Select(c => c.Text);
                            contentValue = string.Join(" . ", docs);
                        }
                        else
                        {
                            doc.Document.TryGetValue("content", out var value);
                            contentValue = value as string;
                        }
                    }
                    catch (ArgumentNullException)
                    {
                        contentValue = null;
                    }

                    if (sourcePageValue is string sourcePage && contentValue is string content)
                    {
                        content = content.Replace('\r', ' ').Replace('\n', ' ');
                        totalContentLength += content.Length + sourcePage.Length;
                        sb.Add(new SectionItem(sourcePage, content));

                        if (totalContentLength > _minimumContextLength && sb.Count >= _top) { break; }
                    }
                }
            }
            return sb.ToArray();
        }

        /// <summary>
        /// Adds sections to the search index.
        /// </summary>
        /// <param name="sections">Sections to add</param>
        /// <returns></returns>
        public async Task AddToSearchAsync(IEnumerable<Section> sections, string PDL = "Default")
        {
            var iteration = 0;
            var batch = new IndexDocumentsBatch<SearchDocument>();
            foreach (var section in sections)
            {
                var embedding = (await _textEmbeddingService.GenerateEmbeddingsAsync(
                    new List<string> { section.Content.Replace('\r', ' ') }, cancellationToken: CancellationToken.None))
                        .ToArray().Select(e => e.ToArray()).SelectMany(e => e).ToArray();

                batch.Actions.Add(new IndexDocumentsAction<SearchDocument>(
                    IndexActionType.MergeOrUpload,
                    new SearchDocument
                    {
                        ["id"] = section.Id,
                        ["content"] = section.Content,
                        ["workspace"] = section.Workspace,
                        ["sourcepage"] = section.SourcePage,
                        ["sourcefile"] = section.SourceFile,
                        ["embedding"] = embedding,
                        ["oids"] = new[] { section.Id.Split('_')[0] },
                        ["groups"] = new[] { section.Id.Split('_')[1] }
                    }));

                iteration++;
                if (iteration % 1_000 is 0)
                {
                    // Every one thousand documents, batch create.
                    IndexDocumentsResult result = await GetSearchClient(PDL).IndexDocumentsAsync(batch);
                    int succeeded = result.Results.Count(r => r.Succeeded);
                    if (_logger.IsEnabled(LogLevel.Information) is true)
                    {
                        _logger.LogInformation("Indexed {Count} sections, {Succeeded} succeeded",
                            batch.Actions.Count, succeeded);
                    }

                    batch = new IndexDocumentsBatch<SearchDocument>();
                }
            }

            if (batch is { Actions.Count: > 0 })
            {
                // Any remaining documents, batch create.
                IndexDocumentsResult result = await GetSearchClient(PDL).IndexDocumentsAsync(batch);
                int succeeded = result.Results.Count(r => r.Succeeded);
                if (_logger.IsEnabled(LogLevel.Information))
                {
                    _logger.LogInformation("Indexed {Count} sections, {Succeeded} succeeded",
                        batch.Actions.Count, succeeded);
                }
            }
        }

        /// <summary>
        /// Creates a filter to restrict results to a user and workspace.
        /// </summary>
        /// <param name="oid"></param>
        /// <param name="workspace"></param>
        /// <returns></returns>
        private static string CreateFilter(string oid, string? workspace)
        {
            return workspace == null
                ? $"oids/any(oid: oid eq '{oid}')"
                : $"oids/any(oid: oid eq '{oid}') and workspace eq '{workspace}'";
        }

        /// <summary>
        /// Creates search options based on the search settings.
        /// </summary>
        /// <param name="settings"></param>
        /// <param name="filter"></param>
        /// <param name="vector"></param>
        /// <returns></returns>
        private static SearchOptions CreateSearchOptions(SearchSettings settings, string filter, float[]? vector)
        {
            var options = new SearchOptions
            {
                Filter = filter,
                Size = 50,
                QueryType = settings.UseSemanticRanker ? SearchQueryType.Semantic : SearchQueryType.Simple,
                SemanticSearch = settings.UseSemanticRanker ? new SemanticSearchOptions
                {
                    SemanticConfigurationName = "default",
                    QueryCaption = new(settings.UseSemanticCaptions
                            ? QueryCaptionType.Extractive
                            : QueryCaptionType.None),
                } : null,
            };

            if (vector != null && settings!.RetrievalMode != RetrievalMode.Text)
            {
                var vectorQuery = new VectorizedQuery(vector)
                {
                    KNearestNeighborsCount = settings.UseSemanticRanker ? 50 : settings!.Top,
                };
                vectorQuery.Fields.Add("embedding");
                options.VectorSearch = new VectorSearchOptions();
                options.VectorSearch.Queries.Add(vectorQuery);
            }

            return options;
        }
    }
}
