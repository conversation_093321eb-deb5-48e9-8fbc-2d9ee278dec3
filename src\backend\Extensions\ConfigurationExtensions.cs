﻿using Backend.Models;

namespace Backend.Extensions
{
    internal static class ConfigurationExtensions
    {
        /// <summary>
        /// Returns a proper Settings object. Will first use the values from the settings provided, 
        /// and for any values left undefined, will default to those defined in the app settings.
        /// </summary>
        /// <param name="config">Configuration object containing the app settings</param>
        /// <param name="settings">Settings values that take first priority</param>
        /// <param name="useWorkspacePrompt">Flag used to determine which default prompt template to use</param>
        /// <returns></returns>
        internal static Settings GetSettings(this IConfiguration config, Settings? settings, bool useWorkspacePrompt)
        {
            return new Settings
            {
                OpenAI = GetOpenAISettings(config, settings?.OpenAI),
                Search = GetSearchSettings(config, settings?.Search),
                PromptTemplate = settings?.PromptTemplate ?? GetDefaultPromptTemplate(config, useWorkspacePrompt),
                SuggestFollowupQuestions = settings?.SuggestFollowupQuestions ?? config.GetValue<bool>("DefaultSettings:Suggest_Followup_Questions"),
            };
        }

        /// <summary>
        /// Sets the OpenAI settings, affecting how the AI will behave.
        /// </summary>
        /// <param name="config">Configuration object containing the app settings</param>
        /// <param name="openAISettings">Settings values that take first priority</param>
        /// <returns></returns>
        private static OpenAISettings GetOpenAISettings(IConfiguration config, OpenAISettings? openAISettings)
        {
            return new OpenAISettings
            {
                Temperature = openAISettings?.Temperature ?? config.GetValue<double>("DefaultSettings:Temperature"),
                TopP = openAISettings?.TopP ?? config.GetValue<double>("DefaultSettings:NucleusSamplingFactor"),
                MaxTokens = openAISettings?.MaxTokens ?? config.GetValue<int>("DefaultSettings:MaxTokens"),
                FrequencyPenalty = openAISettings?.FrequencyPenalty ?? config.GetValue<double>("DefaultSettings:FrequencyPenalty"),
                PresencePenalty = openAISettings?.PresencePenalty ?? config.GetValue<double>("DefaultSettings:PresencePenalty")
            };
        }

        /// <summary>
        /// Sets the Search settings, affecting how the search will behave.
        /// </summary>
        /// <param name="config">Configuration object containing the app settings</param>
        /// <param name="searchSettings">Settings values that take first priority</param>
        /// <returns></returns>
        private static SearchSettings GetSearchSettings(IConfiguration config, SearchSettings? searchSettings)
        {
            return new SearchSettings
            {
                Top = searchSettings?.Top ?? config.GetValue<int>("DefaultSettings:Top"),
                UseSemanticRanker = searchSettings?.UseSemanticRanker ?? config.GetValue<bool>("DefaultSettings:Semantic_Ranker"),
                RetrievalMode = searchSettings?.RetrievalMode ?? config.GetValue<RetrievalMode>("DefaultSettings:Retrieval_Mode"),
                UseSemanticCaptions = searchSettings?.UseSemanticCaptions ?? config.GetValue<bool>("DefaultSettings:Semantic_Captions"),
                MinimumRerankerScore = searchSettings?.MinimumRerankerScore ?? config.GetValue<double>("DefaultSettings:Minimum_Reranker_Score"),
                MinimumSearchScore = searchSettings?.MinimumSearchScore ?? config.GetValue<double>("DefaultSettings:Minimum_Search_Score"),
            };
        }

        /// <summary>
        /// Sets the default prompt template.
        /// </summary>
        /// <param name="config">Configuration object containing the app settings</param>
        /// <param name="useWorkspacePrompt"></param>
        /// <returns></returns>
        private static string GetDefaultPromptTemplate(IConfiguration config, bool useWorkspacePrompt)
        {
            return useWorkspacePrompt
                ? config.GetValue<string>("DefaultSettings:Document_Prompt_Template")!
                : config.GetValue<string>("DefaultSettings:Chat_Prompt_Template")!;
        }
    }
}