﻿using Backend.Models;
using Backend.Models.Context;
using Microsoft.Identity.Web;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;

namespace Backend.Extensions
{
    internal static class ControllerExtensions
    {
        internal static void SetContext(this HttpContext context, int chatCount = 0, bool images = false)
        {
            CurrentContext.User = new User
            {
                OID = context.User.GetObjectId() ?? throw new UnauthorizedAccessException(),
                DisplayName = context.User.GetDisplayName(),
                Images = images,
                ChatCount = chatCount,
                PDL = GetClaimValue(context.Request.Headers["Authorization"]!, "xms_pdl") ?? string.Empty
            };
        }

        internal static string? GetClaimValue(string token, string claimType)
        {
            var bytes = Convert.FromBase64String(
                FromUrlSafeBase64(token.Replace("Bearer ", "").Split('.')[1]));

            var val = Encoding.GetEncoding("ISO-8859-1").GetString(bytes);
            var claims = JsonSerializer.Deserialize<Dictionary<string, object>>(val);
            return claims?[claimType]?.ToString();
        }

        private static string FromUrlSafeBase64(string urlSafeBase64)
        {
            urlSafeBase64 = urlSafeBase64.Replace('-', '+').Replace('_', '/');
            switch (urlSafeBase64.Length % 4)
            {
                case 2: urlSafeBase64 += "=="; break;
                case 3: urlSafeBase64 += "="; break;
            }
            return urlSafeBase64;
        }

        internal static async IAsyncEnumerable<DocumentResult> HandleDocumentProcessingAsync<T>(
            Func<Task<T>> mainFunc,
            Func<Task<List<Document>>> getDocumentsFunc,
            [EnumeratorCancellation] CancellationToken ct)
        {
            var context = CurrentContext.User;

            if (!ct.IsCancellationRequested)
            {
                var initialResponse = await mainFunc();
                yield return new DocumentResult(initialResponse as dynamic);
                CurrentContext.User = context;

                List<Document> unprocessedDocs = initialResponse switch
                {
                    Workspace workspace => workspace.Documents?
                        .Where(doc => doc.Processed == Processed.False).ToList() ?? new List<Document>(),
                    List<Document> documents => documents
                        .Where(doc => doc.Processed == Processed.False).ToList() ?? new List<Document>(),
                    _ => new List<Document>()
                };

                if (unprocessedDocs.Any())
                {
                    var startTime = DateTime.UtcNow;
                    var timeout = TimeSpan.FromSeconds(120);

                    while (DateTime.UtcNow - startTime < timeout && !ct.IsCancellationRequested)
                    {
                        if (!unprocessedDocs.Any(doc => doc.Processed == Processed.False)) { break; }
                        await Task.Delay(TimeSpan.FromSeconds(4), ct);

                        var updatedResponse = await getDocumentsFunc() ?? [];

                        foreach (var doc in unprocessedDocs.Where(doc => doc.Processed == Processed.False))
                        {
                            var d = updatedResponse.FirstOrDefault(d => d.Name == doc.Name && d.Processed != Processed.False);
                            if (d != null)
                            {
                                doc.Processed = d.Processed;
                                yield return new DocumentResult(new List<Document> { doc });
                                CurrentContext.User = context;
                            }
                        }
                    }
                }
            }
        }
    }
}