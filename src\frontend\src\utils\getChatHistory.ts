import { CHAT_MSSG_ROLE_ASSISTANT } from "../constants/constants";
// import { getAllData } from "../db/chatDB";
// import { Stores } from '../constants/dbConstants';
import { setApiCallFlag } from "../features/apiCallFlagSlice";
import { addMessage, clearMessages } from "../features/chatSlice";
import { setCompleteMessage } from "../features/completeMessageSlice";
import { setCurrentChatId } from "../features/currentChatIdSlice";
import { setCurrentWorkspaceId } from "../features/currentWorkspaceIdSlice";
// import { setCurrentWorkspaceId } from "../features/currentWorkspaceIdSlice";
import { AllChatState, CompleteMessageState } from "../interfaces";
import { AppDispatch } from "../store";
// import { chatDecryption } from "./chatDecryption";
// import { apiResponseMessagesTransform } from "./chatMessagesTransform";
import { ChatMessageProps } from "./types";

export const getChatHistory = async(chatId: string, allChatHistory:AllChatState, dispatch: AppDispatch)=>{
    dispatch(setApiCallFlag(false));
    if(!allChatHistory.allChat){
        return [];
    }
    if(Array.isArray(allChatHistory.allChat) && allChatHistory.allChat.length === 0){
        return [];
    }
    
    
    const messages = allChatHistory.allChat[chatId];
    dispatch(clearMessages());
    // console.log("chatData" ,messages);
    messages.forEach(message => {
        return dispatch(addMessage(message));
    });
    const completeMessage = getCompleteMessage(messages);
    dispatch(setCompleteMessage(completeMessage));
    dispatch(setCurrentChatId(chatId));
    dispatch(setCurrentWorkspaceId(messages[0].workspaceId));
    // const allDbData = await getAllData(Stores.Users);
    // if(allDbData && allDbData.length>0){
    //     const hash = getParticularChat(allDbData as EncryptedDataProps[], chatId);
    //     const dbParsedMessage = await chatDecryption(hash, email, token);
    //     const messages = apiResponseMessagesTransform(dbParsedMessage, chatId);
    //     dispatch(clearMessages());
    //     messages.forEach(message => {
    //         dispatch(addMessage(message));
    //     });
        
    //     const completeMessage = getCompleteMessage(messages);
    //     dispatch(setCompleteMessage(completeMessage));
    //     dispatch(setCurrentChatId(chatId));
    // }
}

// const getParticularChat = (allDbData: EncryptedDataProps[], chatId: string): string =>{
//     for(const elem of allDbData){
//         if(elem.id === chatId){
//             return elem.hash
//         }
//     }
//     return ""
// }

const getCompleteMessage = (message: ChatMessageProps[])=>{
    const outputObject : CompleteMessageState = {};

    message.forEach((item, index) => {
        if (item.user === CHAT_MSSG_ROLE_ASSISTANT) {
            outputObject[index] = item.isStreamComplete;
        }
    });

    return outputObject;
}

// const getParticularWorkspaceId =(allDbData: EncryptedDataProps[], chatId: string): string =>{
//     const id ="";
//     for(const elem of allDbData){
//         if(elem.id === chatId){
//             return elem.workspaceId
//         }
//     }
//     return id;
// }