﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Azure.AI.Translation.Text;
using Azure.AI.Translation.Document;
using Azure;
using Moq;
using Xunit;
using Backend.Services;

public class TextTranslationTests
{
    private readonly Mock<DocumentTranslationClient> _mockDocumentTranslationClient;
    private readonly Mock<TextTranslationClient> _mockTextTranslationClient;
    private readonly TranslationService _translationService;

    public TextTranslationTests()
    {

        _mockDocumentTranslationClient = new Mock<DocumentTranslationClient>();
        _mockTextTranslationClient = new Mock<TextTranslationClient>();

        var endpoints = new Dictionary<string, string>
        {
            { "Default", "https://api.cognitive.microsofttranslator.com" }
        };

        _translationService = new TranslationService(
            new Dictionary<string, Azure.Storage.Blobs.BlobContainerClient>(),
            30,
            _mockDocumentTranslationClient.Object,
            _mockTextTranslationClient.Object,
            endpoints
        );
    }


[Fact]
    public async Task NoLanguageSelected()
    {
        string inputText = "Hello";
        string targetLanguage = "";

        await Assert.ThrowsAsync<NullReferenceException>(() => _translationService.TextTranslate(inputText, targetLanguage));
    }

    [Fact]
    public async Task EmptyText()
    {
        string inputText = "";
        string targetLanguage = "fr";

        await Assert.ThrowsAsync<NullReferenceException>(() => _translationService.TextTranslate(inputText, targetLanguage));
    }

    [Fact]
    public async Task NullText()
    {
        string inputText = null;
        string targetLanguage = "fr";

        await Assert.ThrowsAsync<NullReferenceException>(() => _translationService.TextTranslate(inputText, targetLanguage));

    }
}
