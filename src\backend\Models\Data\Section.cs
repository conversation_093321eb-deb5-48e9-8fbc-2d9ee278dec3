﻿namespace Backend.Models
{
    /// <summary>
    /// Represents a section stored within the vector database.
    /// </summary>
    /// <param name="Id">Id</param>
    /// <param name="Content">Content</param>
    /// <param name="Workspace">Workspace</param>
    /// <param name="SourcePage">SourcePage</param>
    /// <param name="SourceFile">SourceFile</param>
    public readonly record struct Section(string Id, string Content, string Workspace, string SourcePage, string SourceFile);
}
