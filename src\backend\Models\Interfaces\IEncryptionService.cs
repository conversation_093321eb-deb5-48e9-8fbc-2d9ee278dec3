﻿namespace Backend.Models
{
    /// <summary>
    /// Interface for Encryption Service
    /// </summary>
    public interface IEncryptionService
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="plainText"></param>
        /// <param name="key"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        string Encrypt(string plainText, string key, string email);

        /// <summary>
        /// 
        /// </summary>
        /// <param name="cipherText"></param>
        /// <param name="key"></param>
        /// <param name="email"></param>
        /// <returns></returns>
        string Decrypt(string cipherText, string key, string email);
    }
}
