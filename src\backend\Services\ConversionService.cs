﻿using Backend.Models;

namespace Backend.Services
{
    public class ConversionService : IConversionService
    {
        private static readonly HttpClient _client = new();
        private static string? _endpoint;
        private static string? _key;

        public ConversionService(string endpoint, string key)
        {
            _endpoint = endpoint;
            _key = key;
        }

        public async Task<Stream> ConvertToPDF(string fileName, Stream stream)
        {
            stream.Seek(0, SeekOrigin.Begin);

            using (var content = new MultipartFormDataContent())
            {
                var contentStream = new MemoryStream();
                await stream.CopyToAsync(contentStream);
                contentStream.Seek(0, SeekOrigin.Begin);

                content.Add(new StreamContent(contentStream), _key!, fileName);

                var response = await _client.PostAsync(_endpoint, content);
                response.EnsureSuccessStatusCode();

                var responseStream = await response.Content.ReadAsStreamAsync();
                return responseStream;
            }
        }
    }
}
